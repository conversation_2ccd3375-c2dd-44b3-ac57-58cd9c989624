import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Pencil, Trash2, CreditCard } from "lucide-react";
import type { Pledge, PaymentStatus } from "@/types/app";

interface PledgeCardProps {
  pledge: Pledge;
  onEdit: (pledge: Pledge) => void;
  onDelete: (pledgeId: string) => void;
  onUpdatePayment: (pledge: Pledge) => void;
}

const PledgeCard = ({ pledge, onEdit, onDelete, onUpdatePayment }: PledgeCardProps) => {
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not paid';
    return new Date(dateString).toLocaleDateString();
  };

  const formatAmount = (amount: number) => {
    return `UGX ${amount.toLocaleString()}`;
  };

  const getStatusColor = (status: PaymentStatus) => {
    switch (status) {
      case 'completed':
        return 'text-green-600';
      case 'partial':
        return 'text-yellow-600';
      default:
        return 'text-red-600';
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle>{pledge.guest_name}</CardTitle>
            <CardDescription>{pledge.guest_phone || 'No phone number'}</CardDescription>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="icon"
              onClick={() => onEdit(pledge)}
            >
              <Pencil className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={() => onDelete(pledge.id)}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-gray-500">Amount Pledged</p>
              <p className="text-lg font-semibold">{formatAmount(pledge.amount_pledged)}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Amount Paid</p>
              <p className="text-lg font-semibold">{formatAmount(pledge.amount_paid)}</p>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-gray-500">Status</p>
              <p className={`text-sm font-medium ${getStatusColor(pledge.payment_status as PaymentStatus)}`}>
                {pledge.payment_status?.charAt(0).toUpperCase() + pledge.payment_status?.slice(1)}
              </p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Pledge Date</p>
              <p className="text-sm">{formatDate(pledge.pledge_date)}</p>
            </div>
          </div>
          {pledge.notes && (
            <div>
              <p className="text-sm font-medium text-gray-500">Notes</p>
              <p className="text-sm">{pledge.notes}</p>
            </div>
          )}
          <Button
            variant="outline"
            className="w-full"
            onClick={() => onUpdatePayment(pledge)}
          >
            <CreditCard className="h-4 w-4 mr-2" />
            Update Payment
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default PledgeCard;
