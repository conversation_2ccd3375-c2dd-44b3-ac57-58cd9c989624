import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { vi } from 'vitest';
import Index from '../Index';

// Mock react-router-dom
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

// Mock Supabase
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    auth: {
      getSession: vi.fn(),
      onAuthStateChange: vi.fn(() => ({
        data: { subscription: { unsubscribe: vi.fn() } },
      })),
    },
  },
}));

// Mock toast
vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({ toast: vi.fn() }),
}));

const renderIndex = () => {
  return render(
    <BrowserRouter>
      <Index />
    </BrowserRouter>
  );
};

describe('Index Page', () => {
  let supabase: any;

  beforeEach(async () => {
    vi.clearAllMocks();
    const module = await import('@/integrations/supabase/client');
    supabase = module.supabase;
  });

  it('renders the main heading and tagline', async () => {
    supabase.auth.getSession.mockResolvedValue({ data: { session: null } });

    renderIndex();

    expect(screen.getByText('PledgeForLove')).toBeInTheDocument();
    expect(screen.getByText(/Beautiful wedding pledge cards/)).toBeInTheDocument();
  });

  it('shows "Create Pledge Card" button when user is not authenticated', async () => {
    supabase.auth.getSession.mockResolvedValue({ data: { session: null } });

    renderIndex();

    await waitFor(() => {
      expect(screen.getByText('Create Pledge Card')).toBeInTheDocument();
    });
  });

  it('shows "Dashboard" button when user is authenticated', async () => {
    supabase.auth.getSession.mockResolvedValue({
      data: { session: { user: { id: 'test-user' } } }
    });

    renderIndex();

    await waitFor(() => {
      expect(screen.getByText('Dashboard')).toBeInTheDocument();
    });
  });

  it('navigates to auth page when "Create Pledge Card" is clicked and user is not authenticated', async () => {
    supabase.auth.getSession.mockResolvedValue({ data: { session: null } });

    renderIndex();

    await waitFor(() => {
      const button = screen.getByText('Create Pledge Card');
      fireEvent.click(button);
    });

    expect(mockNavigate).toHaveBeenCalledWith('/auth');
  });

  it('navigates to dashboard when "Dashboard" is clicked and user is authenticated', async () => {
    supabase.auth.getSession.mockResolvedValue({
      data: { session: { user: { id: 'test-user' } } }
    });

    renderIndex();

    await waitFor(() => {
      const button = screen.getByText('Dashboard');
      fireEvent.click(button);
    });

    expect(mockNavigate).toHaveBeenCalledWith('/dashboard');
  });

  it('renders feature cards', () => {
    supabase.auth.getSession.mockResolvedValue({ data: { session: null } });

    renderIndex();

    expect(screen.getByText('Easy Setup')).toBeInTheDocument();
    expect(screen.getByText('Guest Management')).toBeInTheDocument();
    expect(screen.getByText('Secure & Private')).toBeInTheDocument();
    expect(screen.getByText('Beautiful Design')).toBeInTheDocument();
  });

  it('renders demo button', () => {
    supabase.auth.getSession.mockResolvedValue({ data: { session: null } });

    renderIndex();

    expect(screen.getByText('View Demo')).toBeInTheDocument();
  });

  it('navigates to demo when demo button is clicked', () => {
    supabase.auth.getSession.mockResolvedValue({ data: { session: null } });

    renderIndex();

    const demoButton = screen.getByText('View Demo');
    fireEvent.click(demoButton);

    expect(mockNavigate).toHaveBeenCalledWith('/demo-pledge');
  });

  it('has proper semantic structure', () => {
    supabase.auth.getSession.mockResolvedValue({ data: { session: null } });

    renderIndex();

    // Check for main content structure
    expect(screen.getByRole('main')).toBeInTheDocument();

    // Check for proper heading hierarchy
    const mainHeading = screen.getByRole('heading', { level: 1 });
    expect(mainHeading).toHaveTextContent('PledgeForLove');
  });

  it('handles authentication check errors gracefully', async () => {
    supabase.auth.getSession.mockRejectedValue(new Error('Auth error'));

    renderIndex();

    // Should still render the page with default unauthenticated state
    await waitFor(() => {
      expect(screen.getByText('Create Pledge Card')).toBeInTheDocument();
    });
  });

  it('displays icons correctly', () => {
    supabase.auth.getSession.mockResolvedValue({ data: { session: null } });

    renderIndex();

    // Check for heart and diamond icons in hero section
    const heroSection = screen.getByText('PledgeForLove').closest('div');
    expect(heroSection?.querySelector('svg')).toBeInTheDocument();
  });
});
