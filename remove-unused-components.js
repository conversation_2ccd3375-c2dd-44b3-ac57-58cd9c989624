#!/usr/bin/env node

import fs from 'fs';
import path from 'path';

// List of unused UI components to remove
const unusedComponents = [
  'accordion',
  'aspect-ratio', 
  'avatar',
  'breadcrumb',
  'calendar',
  'carousel',
  'chart',
  'checkbox',
  'collapsible',
  'command',
  'context-menu',
  'drawer',
  'dropdown-menu',
  'hover-card',
  'input-otp',
  'menubar',
  'navigation-menu',
  'pagination',
  'popover',
  'progress',
  'radio-group',
  'resizable',
  'scroll-area',
  'separator',
  'sheet',
  'sidebar',
  'skeleton',
  'slider',
  'tabs',
  'toggle-group',
  'toggle'
];

// Map components to their Radix UI dependencies
const componentToRadixMap = {
  'accordion': '@radix-ui/react-accordion',
  'aspect-ratio': '@radix-ui/react-aspect-ratio',
  'avatar': '@radix-ui/react-avatar',
  'checkbox': '@radix-ui/react-checkbox',
  'collapsible': '@radix-ui/react-collapsible',
  'command': null, // Uses cmdk, not direct radix
  'context-menu': '@radix-ui/react-context-menu',
  'dropdown-menu': '@radix-ui/react-dropdown-menu',
  'hover-card': '@radix-ui/react-hover-card',
  'menubar': '@radix-ui/react-menubar',
  'navigation-menu': '@radix-ui/react-navigation-menu',
  'popover': '@radix-ui/react-popover',
  'progress': '@radix-ui/react-progress',
  'radio-group': '@radix-ui/react-radio-group',
  'scroll-area': '@radix-ui/react-scroll-area',
  'separator': '@radix-ui/react-separator',
  'slider': '@radix-ui/react-slider',
  'tabs': '@radix-ui/react-tabs',
  'toggle-group': '@radix-ui/react-toggle-group',
  'toggle': '@radix-ui/react-toggle'
};

console.log('🗑️  Removing unused UI components...\n');

// Remove unused component files
let removedFiles = 0;
unusedComponents.forEach(component => {
  const filePath = `src/components/ui/${component}.tsx`;
  if (fs.existsSync(filePath)) {
    fs.unlinkSync(filePath);
    console.log(`✅ Removed: ${filePath}`);
    removedFiles++;
  }
});

console.log(`\n📊 Removed ${removedFiles} unused UI component files\n`);

// Collect Radix UI packages to remove
const radixPackagesToRemove = new Set();
unusedComponents.forEach(component => {
  const radixPackage = componentToRadixMap[component];
  if (radixPackage) {
    radixPackagesToRemove.add(radixPackage);
  }
});

// Check if any remaining components use these packages
const remainingComponents = fs.readdirSync('src/components/ui')
  .filter(file => file.endsWith('.tsx'))
  .map(file => file.replace('.tsx', ''));

// Read remaining component files to see what they import
const stillUsedPackages = new Set();
remainingComponents.forEach(component => {
  const filePath = `src/components/ui/${component}.tsx`;
  if (fs.existsSync(filePath)) {
    const content = fs.readFileSync(filePath, 'utf8');
    Array.from(radixPackagesToRemove).forEach(pkg => {
      if (content.includes(pkg)) {
        stillUsedPackages.add(pkg);
      }
    });
  }
});

// Remove packages that are no longer used
const packagesToRemove = Array.from(radixPackagesToRemove).filter(pkg => !stillUsedPackages.has(pkg));

console.log('📦 Radix UI packages that can be removed:');
packagesToRemove.forEach(pkg => console.log(`  - ${pkg}`));

if (packagesToRemove.length > 0) {
  console.log(`\n🚀 Run this command to remove unused packages:`);
  console.log(`npm uninstall ${packagesToRemove.join(' ')}`);
} else {
  console.log('\n✅ No Radix UI packages can be safely removed (some are used by remaining components)');
}

console.log(`\n📈 Bundle size reduction:`);
console.log(`  - Removed ${removedFiles} UI component files`);
console.log(`  - Can remove ${packagesToRemove.length} Radix UI packages`);
