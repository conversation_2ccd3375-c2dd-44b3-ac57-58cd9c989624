import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Heart, HelpCircle } from 'lucide-react';
import { useSEO } from '@/utils/seo';
import { FAQStructuredData, BreadcrumbStructuredData } from '@/components/StructuredData';

const FAQ = () => {
  // SEO optimization for FAQ page
  useSEO('faq', {
    title: "Frequently Asked Questions | PledgeForLove Uganda",
    description: "Get answers to common questions about PledgeForLove Uganda's digital wedding contribution platform. Learn how to create pledge cards, manage contributions, and more.",
    keywords: "FAQ, help, wedding contributions, pledge cards, Uganda wedding platform, digital wedding, questions"
  });

  const faqData = [
    {
      question: "How does PledgeForLove work?",
      answer: "PledgeForLove allows couples to create digital pledge cards for their wedding. Guests can view these cards, make pledges, and contribute to the couple's wedding fund. The platform tracks all contributions and provides real-time updates to the couple."
    },
    {
      question: "Is PledgeForLove free to use?",
      answer: "Yes, PledgeForLove is completely free to use. There are no hidden fees or charges for creating pledge cards, managing contributions, or tracking payments."
    },
    {
      question: "Can I use PledgeForLove for weddings in Uganda?",
      answer: "Absolutely! PledgeForLove is specifically designed for Ugandan weddings. We support local payment methods including mobile money and understand Ugandan wedding traditions and customs."
    },
    {
      question: "How do guests make contributions?",
      answer: "Guests can contribute through various methods including mobile money (MTN Mobile Money, Airtel Money), bank transfers, or cash payments. The platform provides clear instructions for each payment method."
    },
    {
      question: "Can I track who has contributed?",
      answer: "Yes, the platform provides a comprehensive dashboard where you can see all contributions, track payment status, and manage guest pledges in real-time."
    },
    {
      question: "Is my data secure on PledgeForLove?",
      answer: "Security is our top priority. We use industry-standard encryption and security measures to protect your personal information and financial data. Your privacy is always maintained."
    },
    {
      question: "Can I customize my pledge cards?",
      answer: "Yes, you can personalize your pledge cards with your wedding details, photos, and custom messages. The platform offers various templates and customization options."
    },
    {
      question: "What payment methods are supported?",
      answer: "We support multiple payment methods popular in Uganda including MTN Mobile Money, Airtel Money, bank transfers, and cash payments. More payment options are being added regularly."
    },
    {
      question: "Can I share my pledge cards on social media?",
      answer: "Absolutely! The platform includes built-in social sharing features for Facebook, Twitter, WhatsApp, and email. You can easily share your pledge cards with friends and family."
    },
    {
      question: "Do I need technical skills to use PledgeForLove?",
      answer: "Not at all! PledgeForLove is designed to be user-friendly and intuitive. The platform guides you through each step, making it easy for anyone to create and manage their wedding contributions."
    },
    {
      question: "Can I use PledgeForLove on my mobile phone?",
      answer: "Yes, PledgeForLove is fully mobile-responsive and works perfectly on smartphones and tablets. You can manage your wedding contributions from anywhere."
    },
    {
      question: "What happens after my wedding?",
      answer: "Your account and data remain accessible after your wedding. You can continue to track any pending contributions and download reports of all your wedding contributions for your records."
    }
  ];

  // Prepare structured data for FAQ
  const faqStructuredData = {
    questions: faqData.map(item => ({
      "@type": "Question",
      name: item.question,
      acceptedAnswer: {
        "@type": "Answer",
        text: item.answer
      }
    }))
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 to-purple-50 py-12">
      {/* Structured Data */}
      <FAQStructuredData data={faqStructuredData} />
      <BreadcrumbStructuredData 
        items={[
          { name: "Home", url: "https://pledgeforlove.ug" },
          { name: "FAQ", url: "https://pledgeforlove.ug/faq" }
        ]} 
      />

      <div className="max-w-4xl mx-auto px-6">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <div className="bg-pink-100 p-4 rounded-full">
              <HelpCircle className="h-12 w-12 text-pink-600" />
            </div>
          </div>
          <h1 className="text-4xl font-bold text-rose-800 mb-4">
            Frequently Asked Questions
          </h1>
          <p className="text-lg text-rose-700/90 max-w-2xl mx-auto">
            Find answers to common questions about PledgeForLove Uganda's digital wedding contribution platform.
          </p>
        </div>

        {/* FAQ Content */}
        <Card className="bg-white/80 backdrop-blur-sm border-pink-100">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-rose-800">
              <Heart className="h-5 w-5 text-pink-500" />
              Common Questions
            </CardTitle>
            <CardDescription>
              Everything you need to know about using PledgeForLove for your wedding
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Accordion type="single" collapsible className="space-y-2">
              {faqData.map((item, index) => (
                <AccordionItem 
                  key={index} 
                  value={`item-${index}`}
                  className="border border-pink-100 rounded-lg px-4"
                >
                  <AccordionTrigger className="text-left text-rose-800 hover:text-pink-600">
                    {item.question}
                  </AccordionTrigger>
                  <AccordionContent className="text-rose-700/90 pt-2">
                    {item.answer}
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </CardContent>
        </Card>

        {/* Contact Section */}
        <div className="mt-12 text-center">
          <Card className="bg-gradient-to-r from-pink-100 to-purple-100 border-pink-200">
            <CardContent className="pt-6">
              <h2 className="text-2xl font-bold text-rose-800 mb-4">
                Still have questions?
              </h2>
              <p className="text-rose-700/90 mb-6">
                We're here to help! Contact our support team for personalized assistance with your wedding planning.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a 
                  href="mailto:<EMAIL>"
                  className="inline-flex items-center justify-center px-6 py-3 bg-pink-500 text-white rounded-lg hover:bg-pink-600 transition-colors"
                  aria-label="Email support team"
                >
                  Email Support
                </a>
                <a 
                  href="https://wa.me/256XXXXXXXXX"
                  className="inline-flex items-center justify-center px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
                  aria-label="Contact via WhatsApp"
                >
                  WhatsApp Support
                </a>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default FAQ;
