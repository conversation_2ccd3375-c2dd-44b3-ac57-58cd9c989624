
import { useNavigate } from "react-router-dom";
import { useEffect } from "react";

const DemoPledge = () => {
  const navigate = useNavigate();
  
  const demoData = {
    brideName: "<PERSON><PERSON>",
    groomName: "<PERSON>",
    weddingDate: "2024-07-20",
    venue: "Munyonyo Commonwealth Resort",
    treasurer<PERSON><PERSON>: "Grace Nakato",
    treasurer<PERSON><PERSON>: "+256 777 123 456",
    specialMessage: "We are excited to celebrate our union with family and friends. Your support means the world to us as we begin this beautiful journey together.",
    theme: "royal"
  };

  useEffect(() => {
    // Navigate to the pledge card with demo data
    navigate('/pledge/demo', { state: { pledgeData: demoData }, replace: true });
  }, [navigate]);

  return null;
};

export default DemoPledge;