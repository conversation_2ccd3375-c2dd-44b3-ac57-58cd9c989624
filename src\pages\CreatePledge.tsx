
import { useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { ArrowLeft, Share2, Eye } from "lucide-react";
import type { WeddingProfile } from "@/types/app";
import LoadingSpinner from "@/components/ui/loading-spinner";
import { useAuth } from "@/contexts/AuthContext";

const CreatePledge = () => {
  const { user, profile, refreshProfile } = useAuth();
  const [loading, setLoading] = useState(false);
  const [pledgeCardCreated, setPledgeCardCreated] = useState(false);
  const [formData, setFormData] = useState({
    special_message: "Join us in celebrating our special day with your loving support and contributions.",
    theme: "sunset",
  });

  const navigate = useNavigate();
  const { toast } = useToast();

  const themes = {
    sunset: "Sunset Romance",
    royal: "Royal Elegance",
    garden: "Garden Fresh",
    golden: "Golden Glow"
  };



  useEffect(() => {
    // Initialize form data when profile is available
    if (profile) {
      // Check if pledge card is already created (has theme and special_message)
      if (profile.theme && profile.special_message) {
        setPledgeCardCreated(true);
        setFormData({
          special_message: profile.special_message,
          theme: profile.theme
        });
      }
    }
  }, [profile]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    setLoading(true);

    try {
      console.log('Updating profile with pledge card data');
      console.log('Update data:', {
        theme: formData.theme,
        special_message: formData.special_message,
      });

      // Update profile with theme and special message
      const { data: profileData, error } = await supabase
        .from('profiles')
        .update({
          theme: formData.theme,
          special_message: formData.special_message,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id)
        .select()
        .single();

      if (error) {
        console.error('Error updating profile:', error);
        throw error;
      }

      if (!profileData) {
        console.error('No profile data returned after update');
        throw new Error('Failed to update profile');
      }

      console.log('Profile updated successfully:', profileData);
      setPledgeCardCreated(true);

      toast({
        title: "Pledge Card Created! 🎉",
        description: "Your wedding pledge card is ready to share with family and friends.",
      });
      
    } catch (error) {
      console.error('Error creating pledge card:', error);
      if (error instanceof Error) {
        toast({
          title: "Error",
          description: error.message,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Error",
          description: "Failed to create pledge card. Please try again.",
          variant: "destructive",
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const handlePreview = () => {
    if (user) {
      navigate(`/pledge/${user.id}`);
    } else {
      navigate('/demo-pledge');
    }
  };

  const handleShare = async () => {
    if (user) {
      const pledgeUrl = `${window.location.origin}/pledge/${user.id}`;

      if (navigator.share) {
        try {
          await navigator.share({
            title: `${profile?.bride_name} & ${profile?.groom_name}'s Wedding Pledge Card`,
            text: `Join ${profile?.bride_name} & ${profile?.groom_name}'s wedding celebration! Make your pledge here:`,
            url: pledgeUrl
          });
        } catch (err) {
          console.log('Error sharing:', err);
        }
      } else {
        navigator.clipboard.writeText(pledgeUrl);
        toast({
          title: "Link Copied! 📋",
          description: "Share this link with family and friends via WhatsApp, SMS, or email.",
        });
      }
    }
  };

  if (!profile) {
    return (
      <div className="min-h-64">
        <LoadingSpinner variant="card" size="lg" text="Loading your profile..." />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-2xl">
      <div className="mb-6">
        <Button
          variant="ghost"
          onClick={() => navigate('/dashboard')}
          className="mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Dashboard
        </Button>
        <h1 className="text-3xl font-bold text-gray-900">
          {pledgeCardCreated ? 'Manage Pledge Card' : 'Create Pledge Card'}
        </h1>
        <p className="text-gray-600">
          {pledgeCardCreated ? 'Update and share your wedding pledge card with guests' : 'Customize your wedding pledge card to share with family and friends'}
        </p>
      </div>

      {pledgeCardCreated && (
        <Card className="mb-6 bg-green-50 border-green-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold text-green-800">Pledge Card Ready! 🎉</h3>
                <p className="text-sm text-green-600">
                  Your pledge card is ready and can be shared with guests. Anyone can submit pledges without creating an account.
                </p>
              </div>
              <div className="flex gap-2">
                <Button
                  onClick={handlePreview}
                  variant="outline"
                  size="sm"
                  className="border-green-300 text-green-700 hover:bg-green-100"
                >
                  <Eye className="h-4 w-4 mr-2" />
                  Preview
                </Button>
                <Button
                  onClick={handleShare}
                  size="sm"
                  className="bg-green-600 hover:bg-green-700 text-white"
                >
                  <Share2 className="h-4 w-4 mr-2" />
                  Share
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Card Customization</CardTitle>
          <CardDescription>
            Choose a theme and add a special message for your guests
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="theme">Card Theme</Label>
              <Select 
                value={formData.theme} 
                onValueChange={(value) => handleInputChange('theme', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a theme" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(themes).map(([key, label]) => (
                    <SelectItem key={key} value={key}>{label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="special_message">Special Message</Label>
              <Textarea
                id="special_message"
                value={formData.special_message}
                onChange={(e) => handleInputChange('special_message', e.target.value)}
                placeholder="Your message to guests..."
                rows={3}
              />
            </div>

            <div className="flex gap-4">
              <Button type="submit" disabled={loading} className="flex-1">
                {loading ? 'Updating Pledge Card...' : pledgeCardCreated ? 'Update Pledge Card' : 'Create Pledge Card'}
              </Button>
              <Button 
                type="button" 
                variant="outline" 
                onClick={handlePreview}
                disabled={loading}
              >
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default CreatePledge;
