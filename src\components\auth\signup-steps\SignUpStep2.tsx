import { UseFormReturn } from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { SignUpFormData } from "@/components/auth/schemas";

interface SignUpStep2Props {
  form: UseFormReturn<SignUpFormData>;
}

export function SignUpStep2({ form }: SignUpStep2Props) {
  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-semibold text-foreground">Wedding Details</h2>
        <p className="text-muted-foreground mt-2">
          Tell us about the happy couple to personalize your pledge experience
        </p>
      </div>

      <div className="space-y-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="brideName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Bride's Name</FormLabel>
                <FormControl>
                  <Input 
                    {...field} 
                    placeholder="Bride's full name"
                    className="h-11"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="groomName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Groom's Name</FormLabel>
                <FormControl>
                  <Input 
                    {...field} 
                    placeholder="Groom's full name"
                    className="h-11"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="weddingDate"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Wedding Date (Optional)</FormLabel>
              <FormControl>
                <Input 
                  {...field} 
                  type="date" 
                  className="h-11"
                />
              </FormControl>
              <FormMessage />
              <p className="text-xs text-muted-foreground mt-1">
                You can add this later if you haven't set a date yet
              </p>
            </FormItem>
          )}
        />
      </div>

      <div className="bg-muted/50 p-4 rounded-lg">
        <p className="text-sm text-muted-foreground">
          <strong>Almost done!</strong> Next we'll collect some optional contact information to help your friends and family reach you.
        </p>
      </div>
    </div>
  );
}
