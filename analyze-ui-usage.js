#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Get all UI components
const uiDir = 'src/components/ui';
const uiFiles = fs.readdirSync(uiDir).filter(file => file.endsWith('.tsx'));
const uiComponents = uiFiles.map(file => file.replace('.tsx', ''));

console.log('🎨 Available UI components:');
uiComponents.forEach(comp => console.log(`  - ${comp}`));
console.log(`\nTotal: ${uiComponents.length} components\n`);

// Function to recursively find all .tsx and .ts files (excluding ui directory)
function findFiles(dir, extensions = ['.tsx', '.ts']) {
  let files = [];
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && item !== 'node_modules' && item !== 'ui') {
      files = files.concat(findFiles(fullPath, extensions));
    } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
      files.push(fullPath);
    }
  }
  
  return files;
}

// Find all source files (excluding UI components themselves)
const sourceFiles = findFiles('src');

// Track which UI components are actually imported/used
const usedComponents = new Set();
const componentUsage = {};

// Analyze each file
sourceFiles.forEach(file => {
  const content = fs.readFileSync(file, 'utf8');
  
  // Look for imports from @/components/ui/
  const importMatches = content.match(/from ["']@\/components\/ui\/([^"']+)["']/g);
  if (importMatches) {
    importMatches.forEach(match => {
      const component = match.match(/from ["']@\/components\/ui\/([^"']+)["']/)[1];
      usedComponents.add(component);
      if (!componentUsage[component]) {
        componentUsage[component] = [];
      }
      componentUsage[component].push(file);
    });
  }
});

console.log('✅ Used UI components:');
Array.from(usedComponents).sort().forEach(comp => {
  console.log(`  - ${comp}`);
  if (componentUsage[comp]) {
    componentUsage[comp].forEach(file => {
      console.log(`    └─ ${file.replace(process.cwd() + path.sep, '')}`);
    });
  }
});

const unusedComponents = uiComponents.filter(comp => !usedComponents.has(comp));

console.log(`\n❌ Unused UI components (${unusedComponents.length}):`);
unusedComponents.forEach(comp => console.log(`  - ${comp}`));

console.log(`\n📊 Summary:`);
console.log(`  - Used: ${usedComponents.size}/${uiComponents.length} components`);
console.log(`  - Unused: ${unusedComponents.length}/${uiComponents.length} components`);

if (unusedComponents.length > 0) {
  console.log(`\n🗑️  Unused component files that can be removed:`);
  unusedComponents.forEach(comp => {
    console.log(`  - src/components/ui/${comp}.tsx`);
  });
}
