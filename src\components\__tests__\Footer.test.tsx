import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import Footer from '../Footer';

describe('Footer Component', () => {
  it('renders the footer content', () => {
    render(<Footer />);

    expect(screen.getByText(/© 2024 PledgeForLove/)).toBeInTheDocument();
  });

  it('has correct styling classes', () => {
    const { container } = render(<Footer />);
    const footer = container.querySelector('footer');
    
    expect(footer).toHaveClass('bg-gray-50', 'border-t', 'mt-auto');
  });

  it('has proper semantic structure', () => {
    render(<Footer />);
    
    const footer = screen.getByRole('contentinfo');
    expect(footer).toBeInTheDocument();
  });

  it('displays copyright year correctly', () => {
    render(<Footer />);

    // The footer shows 2024, so test for that specific year
    expect(screen.getByText(/2024/)).toBeInTheDocument();
  });
});
