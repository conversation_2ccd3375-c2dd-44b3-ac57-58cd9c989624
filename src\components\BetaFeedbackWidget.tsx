import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { MessageSquare, X, Star } from 'lucide-react';
import { betaMonitoring, type UserFeedback } from '@/utils/betaMonitoring';
import { useToast } from '@/hooks/use-toast';

const BetaFeedbackWidget = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [feedback, setFeedback] = useState<Partial<UserFeedback>>({
    rating: 0,
    category: 'general',
    description: '',
    severity: 'medium'
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  // Only show in beta mode
  if (import.meta.env.VITE_BETA_MODE !== 'true') {
    return null;
  }

  const handleSubmit = async () => {
    if (!feedback.description?.trim() || feedback.rating === 0) {
      toast({
        title: "Incomplete Feedback",
        description: "Please provide a rating and description.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    
    try {
      const completeFeedback: UserFeedback = {
        rating: feedback.rating!,
        category: feedback.category as UserFeedback['category'],
        description: feedback.description,
        page: window.location.pathname,
        severity: feedback.severity as UserFeedback['severity']
      };

      betaMonitoring.submitFeedback(completeFeedback);
      
      toast({
        title: "Feedback Submitted! 🙏",
        description: "Thank you for helping us improve PledgeForLove Uganda.",
      });

      // Reset form
      setFeedback({
        rating: 0,
        category: 'general',
        description: '',
        severity: 'medium'
      });
      setIsOpen(false);
    } catch (error) {
      toast({
        title: "Submission Failed",
        description: "Please try again or contact support.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const StarRating = () => (
    <div className="flex gap-1">
      {[1, 2, 3, 4, 5].map((star) => (
        <button
          key={star}
          type="button"
          onClick={() => setFeedback(prev => ({ ...prev, rating: star }))}
          className={`p-1 rounded transition-colors ${
            star <= (feedback.rating || 0)
              ? 'text-yellow-500 hover:text-yellow-600'
              : 'text-gray-300 hover:text-gray-400'
          }`}
        >
          <Star className="h-5 w-5 fill-current" />
        </button>
      ))}
    </div>
  );

  if (!isOpen) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={() => setIsOpen(true)}
          className="rounded-full h-12 w-12 bg-blue-600 hover:bg-blue-700 shadow-lg"
          size="sm"
        >
          <MessageSquare className="h-5 w-5" />
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-80">
      <Card className="shadow-xl border-2 border-blue-200">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg flex items-center gap-2">
              <MessageSquare className="h-5 w-5 text-blue-600" />
              Beta Feedback
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsOpen(false)}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <p className="text-sm text-gray-600">
            Help us improve PledgeForLove Uganda
          </p>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Rating */}
          <div>
            <label className="text-sm font-medium mb-2 block">
              How would you rate your experience?
            </label>
            <StarRating />
          </div>

          {/* Category */}
          <div>
            <label className="text-sm font-medium mb-2 block">
              Category
            </label>
            <Select
              value={feedback.category}
              onValueChange={(value) => setFeedback(prev => ({ ...prev, category: value }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="bug">🐛 Bug Report</SelectItem>
                <SelectItem value="feature">💡 Feature Request</SelectItem>
                <SelectItem value="usability">🎯 Usability Issue</SelectItem>
                <SelectItem value="performance">⚡ Performance Issue</SelectItem>
                <SelectItem value="general">💬 General Feedback</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Severity */}
          <div>
            <label className="text-sm font-medium mb-2 block">
              Priority
            </label>
            <Select
              value={feedback.severity}
              onValueChange={(value) => setFeedback(prev => ({ ...prev, severity: value }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="low">🟢 Low - Nice to have</SelectItem>
                <SelectItem value="medium">🟡 Medium - Should fix</SelectItem>
                <SelectItem value="high">🟠 High - Important</SelectItem>
                <SelectItem value="critical">🔴 Critical - Urgent</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Description */}
          <div>
            <label className="text-sm font-medium mb-2 block">
              Description
            </label>
            <Textarea
              placeholder="Please describe your feedback in detail..."
              value={feedback.description}
              onChange={(e) => setFeedback(prev => ({ ...prev, description: e.target.value }))}
              rows={4}
              className="resize-none"
            />
          </div>

          {/* Submit Button */}
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting || !feedback.description?.trim() || feedback.rating === 0}
            className="w-full"
          >
            {isSubmitting ? 'Submitting...' : 'Submit Feedback'}
          </Button>

          {/* Beta Info */}
          <div className="text-xs text-gray-500 bg-blue-50 p-2 rounded">
            <strong>Beta Testing:</strong> Your feedback helps us create the perfect wedding pledge platform for Uganda. Thank you! 🙏
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default BetaFeedbackWidget;
