
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Pencil, Trash2 } from "lucide-react";
import type { Pledge, PaymentStatus } from "@/types/app";

interface PledgeTableProps {
  pledges: Pledge[];
  onEditPledge: (pledge: Pledge) => void;
  onDeletePledge: (pledgeId: string) => void;
  onUpdatePayment: (pledge: Pledge) => void;
}

const PledgeTable = ({ pledges, onEditPledge, onDeletePledge, onUpdatePayment }: PledgeTableProps) => {
  const getStatusBadge = (status: PaymentStatus) => {
    const variants = {
      pending: "secondary",
      partial: "default",
      completed: "default"
    } as const;

    const colors = {
      pending: "bg-yellow-100 text-yellow-800",
      partial: "bg-blue-100 text-blue-800", 
      completed: "bg-green-100 text-green-800"
    };

    return (
      <Badge variant={variants[status]} className={colors[status]}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  if (pledges.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No pledges found matching your criteria.</p>
      </div>
    );
  }

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Guest</TableHead>
          <TableHead>Contact</TableHead>
          <TableHead>Pledged</TableHead>
          <TableHead>Paid</TableHead>
          <TableHead>Status</TableHead>
          <TableHead>Date</TableHead>
          <TableHead className="text-right">Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {pledges.map((pledge) => (
          <TableRow key={pledge.id}>
            <TableCell className="font-medium">{pledge.guest_name}</TableCell>
            <TableCell>{pledge.guest_phone || "No phone"}</TableCell>
            <TableCell>UGX {pledge.amount_pledged.toLocaleString()}</TableCell>
            <TableCell>UGX {pledge.amount_paid.toLocaleString()}</TableCell>
            <TableCell>{getStatusBadge(pledge.payment_status as PaymentStatus)}</TableCell>
            <TableCell>{new Date(pledge.pledge_date).toLocaleDateString()}</TableCell>
            <TableCell className="text-right">
              <div className="flex gap-2 justify-end">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onUpdatePayment(pledge)}
                >
                  Payment
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onEditPledge(pledge)}
                >
                  <Pencil className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onDeletePledge(pledge.id)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};

export default PledgeTable;
