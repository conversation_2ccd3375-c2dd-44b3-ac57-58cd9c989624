import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { PhoneInput } from "@/components/ui/phone-input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { useFocusManagement, useKeyboardNavigation } from "@/hooks/use-keyboard-navigation";
import { supabase } from "@/integrations/supabase/client";
import type { PledgeInsert } from "@/types/app";
import { validatePhoneNumber, validateAmount, ValidationError } from "@/types/app";

interface AddPledgeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onPledgeAdded: () => void;
  userId: string;
}

const AddPledgeDialog = ({ open, onOpenChange, onPledgeAdded, userId }: AddPledgeDialogProps) => {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    guestName: "",
    phoneNumber: "",
    email: "",
    pledgeAmount: "",
    paymentDate: "",
    message: ""
  });

  const { toast } = useToast();

  // Focus management for the dialog
  useFocusManagement(open);

  // Handle Escape key to close dialog
  useKeyboardNavigation({
    onEscape: () => onOpenChange(false)
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const validateFormData = () => {
    const errors: string[] = [];

    if (!formData.guestName.trim()) {
      errors.push('Guest name is required');
    }

    if (formData.phoneNumber) {
      const phoneValidation = validatePhoneNumber(formData.phoneNumber);
      if (!phoneValidation.isValid) {
        errors.push(...phoneValidation.errors);
      }
    }

    const amount = Number(formData.pledgeAmount);
    if (isNaN(amount)) {
      errors.push('Invalid pledge amount');
    } else {
      const amountValidation = validateAmount(amount);
      if (!amountValidation.isValid) {
        errors.push(...amountValidation.errors);
      }
    }

    if (errors.length > 0) {
      throw new ValidationError(errors.join(', '));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      validateFormData();

      const pledgeData: PledgeInsert = {
        user_id: userId,
        guest_name: formData.guestName,
        guest_phone: formData.phoneNumber || null,
        amount_pledged: Number(formData.pledgeAmount),
        amount_paid: 0,
        payment_status: 'pending',
        pledge_date: new Date().toISOString(),
        payment_date: formData.paymentDate || null,
        notes: formData.message || null,
      };

      const { error } = await supabase
        .from('pledges')
        .insert(pledgeData);

      if (error) throw error;

      toast({
        title: "Pledge Added! 🎉",
        description: `Successfully added pledge for ${formData.guestName}`,
      });

      // Reset form
      setFormData({
        guestName: "",
        phoneNumber: "",
        email: "",
        pledgeAmount: "",
        paymentDate: "",
        message: ""
      });

      onPledgeAdded();
      onOpenChange(false);
    } catch (error) {
      if (error instanceof ValidationError) {
        toast({
          title: "Validation Error",
          description: error.message,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Error",
          description: "Failed to add pledge. Please try again.",
          variant: "destructive",
        });
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add New Pledge</DialogTitle>
          <DialogDescription>
            Enter the guest's information and pledge details
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="guestName">Guest Name *</Label>
            <Input
              id="guestName"
              value={formData.guestName}
              onChange={(e) => handleInputChange('guestName', e.target.value)}
              placeholder="Enter guest's full name"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="phoneNumber">Phone Number</Label>
            <PhoneInput
              value={formData.phoneNumber}
              onChange={(formatted, normalized) => handleInputChange('phoneNumber', normalized)}
              showValidation={true}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">Email (Optional)</Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              placeholder="<EMAIL>"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="pledgeAmount">Pledge Amount (UGX) *</Label>
            <Input
              id="pledgeAmount"
              type="number"
              value={formData.pledgeAmount}
              onChange={(e) => handleInputChange('pledgeAmount', e.target.value)}
              placeholder="100000"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="paymentDate">Preferred Payment Date</Label>
            <Input
              id="paymentDate"
              type="date"
              value={formData.paymentDate}
              onChange={(e) => handleInputChange('paymentDate', e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="message">Message for the Couple (Optional)</Label>
            <Textarea
              id="message"
              value={formData.message}
              onChange={(e) => handleInputChange('message', e.target.value)}
              placeholder="Guest's message for the couple..."
              rows={3}
            />
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Adding Pledge...' : 'Add Pledge'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddPledgeDialog; 