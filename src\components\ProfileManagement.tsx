
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { User, Calendar, MapPin, Phone, Mail, Heart, Palette } from "lucide-react";
import type { WeddingProfile } from "@/types/app";

interface ProfileManagementProps {
  profile: WeddingProfile;
  onProfileUpdated: (profile: WeddingProfile) => void;
}

const ProfileManagement = ({ profile, onProfileUpdated }: ProfileManagementProps) => {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    bride_name: profile.bride_name || "",
    groom_name: profile.groom_name || "",
    wedding_date: profile.wedding_date || "",
    venue: profile.venue || "",
    treasurer_name: profile.treasurer_name || "",
    treasurer_phone: profile.treasurer_phone || "",
    email: profile.email || "",
    theme: profile.theme || "sunset",
    special_message: profile.special_message || ""
  });

  const { toast } = useToast();

  const themes = {
    sunset: "Sunset Romance",
    royal: "Royal Elegance", 
    garden: "Garden Fresh",
    golden: "Golden Glow"
  };

  useEffect(() => {
    setFormData({
      bride_name: profile.bride_name || "",
      groom_name: profile.groom_name || "",
      wedding_date: profile.wedding_date || "",
      venue: profile.venue || "",
      treasurer_name: profile.treasurer_name || "",
      treasurer_phone: profile.treasurer_phone || "",
      email: profile.email || "",
      theme: profile.theme || "sunset",
      special_message: profile.special_message || ""
    });
  }, [profile]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        toast({
          title: "Authentication Required",
          description: "Please sign in to update your profile",
          variant: "destructive",
        });
        return;
      }

      const { data, error } = await supabase
        .from('profiles')
        .update({
          ...formData,
          updated_at: new Date().toISOString(),
        })
        .eq('id', session.user.id)
        .select()
        .single();

      if (error) throw error;

      toast({
        title: "Profile Updated! ✨",
        description: "Your wedding profile has been updated successfully.",
      });

      onProfileUpdated(data);
    } catch (error) {
      console.error('Error updating profile:', error);
      toast({
        title: "Error",
        description: "Failed to update profile. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Couple Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Heart className="h-5 w-5" />
            Couple Details
          </CardTitle>
          <CardDescription>Basic information about the bride and groom</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4 sm:space-y-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
            <div className="space-y-2">
              <Label htmlFor="bride_name">Bride's Name</Label>
              <Input
                id="bride_name"
                value={formData.bride_name}
                onChange={(e) => handleInputChange('bride_name', e.target.value)}
                placeholder="Enter bride's full name"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="groom_name">Groom's Name</Label>
              <Input
                id="groom_name"
                value={formData.groom_name}
                onChange={(e) => handleInputChange('groom_name', e.target.value)}
                placeholder="Enter groom's full name"
                required
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Wedding Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Wedding Details
          </CardTitle>
          <CardDescription>When and where the wedding will take place</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4 sm:space-y-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
            <div className="space-y-2">
              <Label htmlFor="wedding_date">Wedding Date</Label>
              <Input
                id="wedding_date"
                type="date"
                value={formData.wedding_date}
                onChange={(e) => handleInputChange('wedding_date', e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="venue">Venue</Label>
              <Input
                id="venue"
                value={formData.venue}
                onChange={(e) => handleInputChange('venue', e.target.value)}
                placeholder="Wedding venue name"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Treasurer Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Treasurer Information
          </CardTitle>
          <CardDescription>Contact details for the person managing pledges</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4 sm:space-y-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
            <div className="space-y-2">
              <Label htmlFor="treasurer_name">Treasurer Name</Label>
              <Input
                id="treasurer_name"
                value={formData.treasurer_name}
                onChange={(e) => handleInputChange('treasurer_name', e.target.value)}
                placeholder="Enter treasurer's name"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="treasurer_phone">Treasurer Phone</Label>
              <Input
                id="treasurer_phone"
                value={formData.treasurer_phone}
                onChange={(e) => handleInputChange('treasurer_phone', e.target.value)}
                placeholder="+256 XXX XXX XXX"
              />
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="email">Email Address</Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              placeholder="<EMAIL>"
              required
            />
          </div>
        </CardContent>
      </Card>

      {/* Card Customization */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Card Customization
          </CardTitle>
          <CardDescription>Customize the appearance and message of your pledge card</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="theme">Card Theme</Label>
            <Select value={formData.theme} onValueChange={(value) => handleInputChange('theme', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select a theme" />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(themes).map(([key, label]) => (
                  <SelectItem key={key} value={key}>{label}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="special_message">Special Message for Guests</Label>
            <Textarea
              id="special_message"
              value={formData.special_message}
              onChange={(e) => handleInputChange('special_message', e.target.value)}
              placeholder="Your message to guests..."
              rows={4}
            />
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-end">
        <Button type="submit" disabled={loading} size="lg">
          {loading ? 'Updating Profile...' : 'Update Profile'}
        </Button>
      </div>
    </form>
  );
};

export default ProfileManagement;
