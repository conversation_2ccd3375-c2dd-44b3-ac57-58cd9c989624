#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Get all Radix UI packages from package.json
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const radixPackages = Object.keys(packageJson.dependencies).filter(pkg => pkg.startsWith('@radix-ui/'));

console.log('📦 Installed Radix UI packages:');
radixPackages.forEach(pkg => console.log(`  - ${pkg}`));
console.log(`\nTotal: ${radixPackages.length} packages\n`);

// Function to recursively find all .tsx and .ts files
function findFiles(dir, extensions = ['.tsx', '.ts']) {
  let files = [];
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && item !== 'node_modules') {
      files = files.concat(findFiles(fullPath, extensions));
    } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
      files.push(fullPath);
    }
  }
  
  return files;
}

// Find all source files
const sourceFiles = findFiles('src');

// Track which packages are actually imported/used
const usedPackages = new Set();
const packageUsage = {};

// Analyze each file
sourceFiles.forEach(file => {
  const content = fs.readFileSync(file, 'utf8');
  
  radixPackages.forEach(pkg => {
    if (content.includes(pkg)) {
      usedPackages.add(pkg);
      if (!packageUsage[pkg]) {
        packageUsage[pkg] = [];
      }
      packageUsage[pkg].push(file);
    }
  });
});

console.log('✅ Used Radix UI packages:');
Array.from(usedPackages).sort().forEach(pkg => {
  console.log(`  - ${pkg}`);
  packageUsage[pkg].forEach(file => {
    console.log(`    └─ ${file.replace(process.cwd() + path.sep, '')}`);
  });
});

const unusedPackages = radixPackages.filter(pkg => !usedPackages.has(pkg));

console.log(`\n❌ Unused Radix UI packages (${unusedPackages.length}):`);
unusedPackages.forEach(pkg => console.log(`  - ${pkg}`));

console.log(`\n📊 Summary:`);
console.log(`  - Used: ${usedPackages.size}/${radixPackages.length} packages`);
console.log(`  - Unused: ${unusedPackages.length}/${radixPackages.length} packages`);

if (unusedPackages.length > 0) {
  console.log(`\n🗑️  To remove unused packages, run:`);
  console.log(`npm uninstall ${unusedPackages.join(' ')}`);
}
