import { useState } from "react";
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Keyboard } from "lucide-react";
import { useFocusManagement, useKeyboardNavigation } from "@/hooks/use-keyboard-navigation";

const KeyboardShortcutsHelp = () => {
  const [open, setOpen] = useState(false);

  // Focus management for the dialog
  useFocusManagement(open);
  
  // Handle Escape key to close dialog
  useKeyboardNavigation({
    onEscape: () => setOpen(false)
  });

  const shortcuts = [
    { key: "Alt + H", description: "Go to Home page" },
    { key: "Alt + D", description: "Go to Dashboard" },
    { key: "Alt + P", description: "Go to Profile" },
    { key: "Alt + C", description: "Go to Create Pledge" },
    { key: "Alt + A", description: "Go to Auth/Sign In" },
    { key: "Escape", description: "Close dialogs and modals" },
    { key: "Tab", description: "Navigate between interactive elements" },
    { key: "Shift + Tab", description: "Navigate backwards between elements" },
    { key: "Enter", description: "Activate buttons and links" },
    { key: "Space", description: "Activate buttons" },
  ];

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button 
          variant="ghost" 
          size="sm"
          aria-label="View keyboard shortcuts"
          className="fixed bottom-4 right-4 z-40 bg-white shadow-lg border"
        >
          <Keyboard className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-md" aria-describedby="keyboard-shortcuts-description">
        <DialogHeader>
          <DialogTitle>Keyboard Shortcuts</DialogTitle>
          <DialogDescription id="keyboard-shortcuts-description">
            Use these keyboard shortcuts to navigate the application more efficiently.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-3">
          {shortcuts.map((shortcut, index) => (
            <div key={index} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
              <span className="text-sm text-gray-600">{shortcut.description}</span>
              <kbd className="px-2 py-1 text-xs font-mono bg-gray-100 border border-gray-300 rounded">
                {shortcut.key}
              </kbd>
            </div>
          ))}
        </div>
        <div className="mt-4 p-3 bg-blue-50 rounded-md">
          <p className="text-xs text-blue-800">
            <strong>Note:</strong> Global shortcuts (Alt + key) work when no input field is focused.
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default KeyboardShortcutsHelp;
