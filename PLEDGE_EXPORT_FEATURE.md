# 📊 Pledge Export Feature

## Overview
The Pledge Export feature allows users to export their pledge data to Excel (.xlsx) and CSV formats with advanced filtering and customization options.

## Features

### ✅ **Quick Export Options**
- **Excel Export (.xlsx)**: Full-featured export with multiple sheets
- **CSV Export**: Simple comma-separated values format
- **One-click export** with auto-generated filenames

### ✅ **Advanced Export Options**
- **Custom filename** specification
- **Payment status filtering** (pending, partial, completed)
- **Date range filtering** (coming soon)
- **Summary sheet** with statistics and metrics
- **Export preview** before downloading

### ✅ **Export Data Includes**
- Guest name and contact information
- Pledge amounts and payment details
- Payment status and dates
- Notes and additional information
- Calculated balances and totals

## How to Use

### **Quick Export**
1. Navigate to **Dashboard → Pledges** tab
2. Click the **Export** button (download icon)
3. Choose **Export to Excel (.xlsx)** or **Export to CSV**
4. File downloads automatically with current date in filename

### **Advanced Export**
1. Click **Export** button → **Advanced Export...**
2. Customize options:
   - **Custom Filename**: Enter desired filename (optional)
   - **Include Summary**: Toggle summary sheet with statistics
   - **Status Filter**: Select which payment statuses to include
3. Review export preview
4. Click **Export Excel** to download

## Export File Structure

### **Excel Export (.xlsx)**
The Excel export includes two sheets:

#### **Sheet 1: Pledges**
| Column | Description |
|--------|-------------|
| No. | Sequential number |
| Guest Name | Full name of the guest |
| Phone Number | Contact phone number |
| Amount Pledged (UGX) | Original pledge amount |
| Amount Paid (UGX) | Amount already paid |
| Balance (UGX) | Remaining balance |
| Payment Status | pending/partial/completed |
| Pledge Date | Date pledge was made |
| Payment Date | Date of payment (if any) |
| Notes | Additional notes |

#### **Sheet 2: Summary** (if enabled)
- Total number of pledges
- Total amount pledged
- Total amount paid
- Outstanding balance
- Collection rate percentage
- Payment status breakdown
- Export metadata

### **CSV Export**
Simple comma-separated format with the same data as the Pledges sheet.

## Technical Implementation

### **Files Added/Modified**
- `src/utils/exportUtils.ts` - Core export functionality
- `src/components/PledgeExportButton.tsx` - Export UI component
- `src/components/ui/dropdown-menu.tsx` - Dropdown menu component
- `src/components/ui/checkbox.tsx` - Checkbox component
- `src/components/PledgeManagement.tsx` - Integrated export button

### **Dependencies Added**
- `xlsx` - Excel file generation
- `@types/xlsx` - TypeScript definitions
- `@radix-ui/react-dropdown-menu` - Dropdown menu primitives
- `@radix-ui/react-checkbox` - Checkbox primitives

### **Key Functions**

#### `exportPledgesToXLSX(pledges, filename?, includeMetadata?)`
Exports pledges to Excel format with optional summary sheet.

#### `exportPledgesToCSV(pledges, filename?)`
Exports pledges to CSV format for simple data exchange.

#### `exportPledgesWithOptions(pledges, options)`
Advanced export with filtering and customization options.

## Usage Examples

### **Basic Export**
```typescript
import { exportPledgesToXLSX } from '@/utils/exportUtils';

// Export all pledges to Excel
const result = exportPledgesToXLSX(pledges);
if (result.success) {
  console.log(`Exported ${result.recordCount} pledges to ${result.filename}`);
}
```

### **Advanced Export with Filters**
```typescript
import { exportPledgesWithOptions } from '@/utils/exportUtils';

// Export only completed payments from last month
const result = exportPledgesWithOptions(pledges, {
  filename: 'completed_pledges_january.xlsx',
  statusFilter: ['completed'],
  includeMetadata: true
});
```

## Benefits

### **For Users**
- **Easy data backup** and archival
- **Share data** with treasurers or wedding planners
- **Import into accounting** software
- **Generate reports** for wedding planning
- **Track payment progress** offline

### **For Developers**
- **Modular design** - easy to extend
- **Type-safe** implementation
- **Error handling** with user feedback
- **Customizable** export options
- **Well-tested** functionality

## Future Enhancements

### **Planned Features**
- 📅 **Date range filtering** in advanced export
- 📧 **Email export** directly to treasurers
- 📈 **Chart generation** in Excel exports
- 🎨 **Custom formatting** options
- 📱 **Mobile-optimized** export interface
- 🔄 **Scheduled exports** (auto-backup)

### **Integration Opportunities**
- **Google Sheets** export
- **PDF report** generation
- **Email integration** for sharing
- **Cloud storage** backup
- **Accounting software** integration

## Testing

Run the export utility tests:
```bash
npm test src/utils/__tests__/exportUtils.test.ts
```

## Support

The export feature is fully integrated into the pledge management system and available to all users with pledge data. For technical support or feature requests, please refer to the main application documentation.

---

**Note**: Export functionality requires modern browser support for file downloads and may be subject to browser security policies.
