
import { useEffect } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, useLocation } from "react-router-dom";
import ErrorBoundary from "@/components/ErrorBoundary";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import OfflineIndicator from "@/components/OfflineIndicator";
import KeyboardShortcutsHelp from "@/components/KeyboardShortcutsHelp";
import AppRoutes from "@/components/AppRoutes";
import { AuthProvider } from "@/contexts/AuthContext";
import { AuthModalProvider, useAuthModal } from "@/contexts/AuthModalContext";
import { AuthModal } from "@/components/auth/AuthModal";
import BetaFeedbackWidget from "@/components/BetaFeedbackWidget";
import { betaMonitoring } from "@/utils/betaMonitoring";

const queryClient = new QueryClient();

// Component to conditionally render Header and Footer
const ConditionalLayout = () => {
  const location = useLocation();
  const isPledgeCard = location.pathname.startsWith('/pledge/');
  const { isAuthModalOpen, authModalMode, closeAuthModal } = useAuthModal();

  return (
    <>
      {/* Skip links for keyboard navigation */}
      <a
        href="#main-content"
        className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded-md z-50"
      >
        Skip to main content
      </a>
      {!isPledgeCard && <Header />}
      <main id="main-content" className="flex-1" tabIndex={-1}>
        <AppRoutes />
      </main>
      <KeyboardShortcutsHelp />
      {!isPledgeCard && <Footer />}

      {/* Auth Modal */}
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={closeAuthModal}
        defaultMode={authModalMode}
      />

      {/* Beta Feedback Widget */}
      <BetaFeedbackWidget />
    </>
  );
};

const App = () => {
  useEffect(() => {
    // Temporarily disable service worker to fix Response conversion error
    // TODO: Re-enable after fixing service worker issues
    if ('serviceWorker' in navigator) {
      // Unregister existing service workers
      navigator.serviceWorker.getRegistrations().then((registrations) => {
        registrations.forEach((registration) => {
          registration.unregister();
          console.log('SW unregistered: ', registration);
        });
      });
    }
  }, []);

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <TooltipProvider>
          <div className="min-h-screen flex flex-col">
            <Toaster />
            <Sonner />
            <OfflineIndicator />
            <BrowserRouter>
              <AuthProvider>
                <AuthModalProvider>
                  <ConditionalLayout />
                </AuthModalProvider>
              </AuthProvider>
            </BrowserRouter>
          </div>
        </TooltipProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
};

export default App;
