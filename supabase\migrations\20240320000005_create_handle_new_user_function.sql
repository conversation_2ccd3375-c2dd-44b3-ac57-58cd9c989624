-- Create the missing handle_new_user() function
-- This function automatically creates a profile record when a new user signs up

CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Insert a new profile record for the newly created user
  INSERT INTO public.profiles (
    id,
    bride_name,
    groom_name,
    wedding_date,
    treasurer_name,
    email,
    theme,
    special_message,
    created_at,
    updated_at
  )
  VALUES (
    NEW.id,
    'Bride Name',  -- Default placeholder
    'Groom Name',  -- Default placeholder
    CURRENT_DATE + INTERVAL '30 days',  -- Default to 30 days from now
    'Treasurer Name',  -- Default placeholder
    COALESCE(NEW.email, ''),  -- Use the user's email or empty string
    'sunset',  -- Default theme
    'Join us in celebrating our special day with your loving support and contributions.',  -- Default message
    NOW(),
    NOW()
  );
  
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error but don't fail the user creation
    RAISE WARNING 'Failed to create profile for user %: %', NEW.id, SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- <PERSON> execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.handle_new_user() TO authenticated;
