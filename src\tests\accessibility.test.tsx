import { render } from '@testing-library/react';
import { axe, toHaveNoViolations } from 'jest-axe';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { TooltipProvider } from '@/components/ui/tooltip';

// Import components to test
import Index from '@/pages/Index';
import Auth from '@/pages/Auth';
import Dashboard from '@/pages/Dashboard';
import CreatePledge from '@/pages/CreatePledge';
import Profile from '@/pages/Profile';
import Header from '@/components/Header';
import PledgeStats from '@/components/PledgeStats';
import OfflineIndicator from '@/components/OfflineIndicator';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <TooltipProvider>
          {children}
        </TooltipProvider>
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('Accessibility Tests', () => {
  beforeEach(() => {
    // Mock console.error to avoid noise in tests
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Page Components', () => {
    test('Index page should not have accessibility violations', async () => {
      const { container } = render(
        <TestWrapper>
          <Index />
        </TestWrapper>
      );
      
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    test('Auth page should not have accessibility violations', async () => {
      const { container } = render(
        <TestWrapper>
          <Auth />
        </TestWrapper>
      );
      
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    // Note: Dashboard, CreatePledge, and Profile require authentication
    // These would need to be tested with mocked auth state
  });

  describe('Component Accessibility', () => {
    test('Header component should not have accessibility violations', async () => {
      const { container } = render(
        <TestWrapper>
          <Header />
        </TestWrapper>
      );
      
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    test('PledgeStats component should not have accessibility violations', async () => {
      const mockPledges = [
        { amount: 100000, status: 'paid' as const },
        { amount: 50000, status: 'pending' as const },
      ];

      const { container } = render(
        <TestWrapper>
          <PledgeStats 
            totalPledged={150000}
            totalPaid={100000}
            pendingAmount={50000}
          />
        </TestWrapper>
      );
      
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    test('OfflineIndicator component should not have accessibility violations', async () => {
      const { container } = render(
        <TestWrapper>
          <OfflineIndicator />
        </TestWrapper>
      );
      
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });

  describe('Keyboard Navigation', () => {
    test('Header navigation should be keyboard accessible', () => {
      const { container } = render(
        <TestWrapper>
          <Header />
        </TestWrapper>
      );

      // Check for focusable elements
      const focusableElements = container.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );
      
      expect(focusableElements.length).toBeGreaterThan(0);
      
      // Check that all buttons have accessible names
      const buttons = container.querySelectorAll('button');
      buttons.forEach(button => {
        const hasAccessibleName = 
          button.textContent?.trim() ||
          button.getAttribute('aria-label') ||
          button.getAttribute('aria-labelledby');
        expect(hasAccessibleName).toBeTruthy();
      });
    });
  });

  describe('ARIA Implementation', () => {
    test('Interactive elements should have proper ARIA attributes', () => {
      const { container } = render(
        <TestWrapper>
          <Header />
        </TestWrapper>
      );

      // Check buttons have proper labels
      const buttons = container.querySelectorAll('button');
      buttons.forEach(button => {
        const hasLabel = 
          button.getAttribute('aria-label') ||
          button.getAttribute('aria-labelledby') ||
          button.textContent?.trim();
        expect(hasLabel).toBeTruthy();
      });
    });

    test('Status information should have proper ARIA labels', () => {
      const { container } = render(
        <TestWrapper>
          <PledgeStats 
            totalPledged={150000}
            totalPaid={100000}
            pendingAmount={50000}
          />
        </TestWrapper>
      );

      // Check for ARIA labels on status elements
      const statusElements = container.querySelectorAll('[aria-label*="Total"], [aria-label*="Pending"]');
      expect(statusElements.length).toBeGreaterThan(0);
    });
  });

  describe('Heading Structure', () => {
    test('Pages should have proper heading hierarchy', () => {
      const { container } = render(
        <TestWrapper>
          <Index />
        </TestWrapper>
      );

      // Check for h1 element
      const h1Elements = container.querySelectorAll('h1');
      expect(h1Elements.length).toBe(1);

      // Check heading hierarchy (h1 should come before h2, etc.)
      const headings = container.querySelectorAll('h1, h2, h3, h4, h5, h6');
      let previousLevel = 0;
      
      headings.forEach(heading => {
        const currentLevel = parseInt(heading.tagName.charAt(1));
        // Allow same level or one level deeper
        expect(currentLevel).toBeLessThanOrEqual(previousLevel + 1);
        previousLevel = currentLevel;
      });
    });
  });

  describe('Form Accessibility', () => {
    test('Form inputs should have associated labels', () => {
      const { container } = render(
        <TestWrapper>
          <Auth />
        </TestWrapper>
      );

      const inputs = container.querySelectorAll('input[type="email"], input[type="password"]');
      inputs.forEach(input => {
        const hasLabel = 
          input.getAttribute('aria-label') ||
          input.getAttribute('aria-labelledby') ||
          container.querySelector(`label[for="${input.id}"]`);
        expect(hasLabel).toBeTruthy();
      });
    });
  });

  describe('Color and Contrast', () => {
    test('Color-coded information should have additional indicators', () => {
      const { container } = render(
        <TestWrapper>
          <PledgeStats 
            totalPledged={150000}
            totalPaid={100000}
            pendingAmount={50000}
          />
        </TestWrapper>
      );

      // Check for icons that supplement color information
      const icons = container.querySelectorAll('svg[aria-hidden="true"]');
      expect(icons.length).toBeGreaterThan(0);
    });
  });
});

// Integration test for full page accessibility
describe('Full Page Accessibility Integration', () => {
  test('Complete Index page with navigation should be accessible', async () => {
    const { container } = render(
      <TestWrapper>
        <div>
          <Header />
          <main id="main-content" tabIndex={-1}>
            <Index />
          </main>
        </div>
      </TestWrapper>
    );

    const results = await axe(container, {
      rules: {
        // Enable all accessibility rules
        'color-contrast': { enabled: true },
        'keyboard-navigation': { enabled: true },
        'focus-management': { enabled: true },
      }
    });

    expect(results).toHaveNoViolations();
  });
});
