# Screen Reader Testing Guide for Love Pledge Uganda

## Overview
This guide provides comprehensive instructions for testing the Love Pledge Uganda application with screen readers to ensure accessibility compliance.

## Screen Reader Tools

### Windows
- **NVDA (Free)**: Download from https://www.nvaccess.org/
- **JAWS**: Commercial screen reader (trial available)
- **Windows Narrator**: Built into Windows 10/11

### macOS
- **VoiceOver**: Built into macOS (Cmd+F5 to enable)

### Mobile
- **TalkBack (Android)**: Settings > Accessibility > TalkBack
- **VoiceOver (iOS)**: Settings > Accessibility > VoiceOver

## Testing Checklist

### 1. Navigation Testing
- [ ] **Skip Links**: Verify "Skip to main content" link works
- [ ] **Heading Structure**: Ensure logical heading hierarchy (h1 → h2 → h3)
- [ ] **Landmark Navigation**: Test navigation between header, main, footer
- [ ] **Tab Order**: Verify logical tab sequence through interactive elements

### 2. Content Testing
- [ ] **Page Titles**: Each page has descriptive title
- [ ] **Headings**: All headings are properly announced
- [ ] **Links**: Link purpose is clear from context
- [ ] **Buttons**: Button labels clearly describe their action
- [ ] **Form Labels**: All form inputs have associated labels

### 3. Interactive Elements
- [ ] **Buttons**: Properly announced with role and state
- [ ] **Form Controls**: Labels, required fields, and error messages announced
- [ ] **Dialogs/Modals**: Focus management and escape functionality
- [ ] **Status Messages**: Live regions announce dynamic content changes

### 4. Data Tables (if applicable)
- [ ] **Table Headers**: Column and row headers properly associated
- [ ] **Table Caption**: Descriptive caption provided
- [ ] **Complex Tables**: Scope attributes used correctly

## Page-by-Page Testing

### Home Page (/)
**Key Elements to Test:**
- Main heading "PledgeForLove"
- Navigation menu accessibility
- Call-to-action buttons
- Feature descriptions

**Expected Announcements:**
- "PledgeForLove, heading level 1"
- "Transform your wedding contributions with beautiful digital pledge cards"
- Navigation buttons with clear labels

### Authentication Page (/auth)
**Key Elements to Test:**
- Form labels and inputs
- Error message announcements
- Submit button functionality

**Expected Announcements:**
- "Email, edit text" (for email input)
- "Password, edit text, password" (for password input)
- Error messages in live regions

### Dashboard (/dashboard)
**Key Elements to Test:**
- Statistics cards with proper labels
- Pledge list navigation
- Action buttons

**Expected Announcements:**
- "Total Pledged: UGX [amount]" with proper context
- "Paid" status with checkmark icon description
- "Pending" status with clock icon description

### Create Pledge (/create-pledge)
**Key Elements to Test:**
- Form validation messages
- Required field indicators
- Success/error feedback

### Profile (/profile)
**Key Elements to Test:**
- Profile information display
- Edit form accessibility
- Save confirmation messages

## Common Screen Reader Commands

### NVDA (Windows)
- **Start/Stop**: Ctrl + Alt + N
- **Navigate by heading**: H (next), Shift + H (previous)
- **Navigate by link**: K (next), Shift + K (previous)
- **Navigate by button**: B (next), Shift + B (previous)
- **Navigate by form field**: F (next), Shift + F (previous)
- **Read all**: Ctrl + A

### VoiceOver (macOS)
- **Start/Stop**: Cmd + F5
- **Navigate**: Ctrl + Option + Arrow keys
- **Rotor**: Ctrl + Option + U (then arrow keys to navigate)
- **Read all**: Ctrl + Option + A

## Testing Scenarios

### Scenario 1: New User Registration
1. Navigate to auth page using keyboard only
2. Fill out registration form using screen reader
3. Verify error messages are announced
4. Complete successful registration

### Scenario 2: Creating a Pledge
1. Navigate to create pledge page
2. Fill out pledge form with screen reader
3. Verify form validation announcements
4. Submit and verify success message

### Scenario 3: Viewing Dashboard
1. Navigate to dashboard
2. Review pledge statistics using screen reader
3. Navigate through pledge list
4. Test action buttons (edit, delete)

## Accessibility Issues to Look For

### Critical Issues
- Missing or incorrect heading structure
- Form inputs without labels
- Images without alt text
- Buttons without accessible names
- Focus not visible or trapped

### Medium Issues
- Color-only information without text alternatives
- Missing live regions for dynamic content
- Inconsistent navigation patterns
- Poor error message association

### Minor Issues
- Redundant or verbose announcements
- Missing skip links
- Inconsistent language or terminology

## Testing Tools Integration

### Automated Testing
```bash
# Run accessibility audit script
node accessibility-audit.js

# Install and run axe-core
npm install --save-dev @axe-core/react
# Add to test files for automated checks
```

### Browser Extensions
- **axe DevTools**: Chrome/Firefox extension for accessibility testing
- **WAVE**: Web accessibility evaluation tool
- **Lighthouse**: Built into Chrome DevTools

## Reporting Issues

When reporting accessibility issues, include:
1. **Screen reader used** (NVDA, VoiceOver, etc.)
2. **Browser and version**
3. **Steps to reproduce**
4. **Expected vs actual behavior**
5. **Severity level** (Critical, Medium, Minor)

## Best Practices Implemented

✅ **Semantic HTML**: Using proper heading hierarchy and landmarks
✅ **ARIA Labels**: Added to interactive elements and status information
✅ **Focus Management**: Proper focus handling in dialogs and modals
✅ **Keyboard Navigation**: Full keyboard accessibility with shortcuts
✅ **Live Regions**: Dynamic content announcements
✅ **Skip Links**: Quick navigation to main content
✅ **Color Independence**: Icons and text supplement color information

## Next Steps

1. **Manual Testing**: Complete the testing checklist with actual screen readers
2. **User Testing**: Involve users with disabilities in testing process
3. **Continuous Monitoring**: Regular accessibility audits during development
4. **Training**: Ensure development team understands accessibility principles

## Resources

- [WCAG 2.1 Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
- [ARIA Authoring Practices](https://www.w3.org/WAI/ARIA/apg/)
- [WebAIM Screen Reader Testing](https://webaim.org/articles/screenreader_testing/)
- [NVDA User Guide](https://www.nvaccess.org/files/nvda/documentation/userGuide.html)
