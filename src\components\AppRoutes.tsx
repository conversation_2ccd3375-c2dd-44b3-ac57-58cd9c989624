import { Suspense, lazy } from "react";
import { Routes, Route } from "react-router-dom";
import { useKeyboardNavigation } from "@/hooks/use-keyboard-navigation";
import ErrorBoundary from "@/components/ErrorBoundary";
import LoadingSpinner from "@/components/ui/loading-spinner";
import ProtectedRoute from "@/components/ProtectedRoute";
import AdminRoute from "@/components/AdminRoute";
import { useAuthModal } from "@/contexts/AuthModalContext";

// Lazy load route components for code splitting
const Index = lazy(() => import("../pages/Index"));
const Auth = lazy(() => import("../pages/Auth"));
const Dashboard = lazy(() => import("../pages/Dashboard"));
const Profile = lazy(() => import("../pages/Profile"));
const ProfileSetup = lazy(() => import("../pages/ProfileSetup"));
const CreatePledge = lazy(() => import("../pages/CreatePledge"));
const PledgeCard = lazy(() => import("../pages/PledgeCard"));
const DemoPledge = lazy(() => import("../pages/DemoPledge"));
const FAQ = lazy(() => import("../pages/FAQ"));
const AdminPanel = lazy(() => import("../pages/AdminPanel"));
const AdminSetupGuide = lazy(() => import("../components/admin/AdminSetupGuide"));
const AdminWelcome = lazy(() => import("../components/admin/AdminWelcome"));
const AdminDiagnostic = lazy(() => import("../components/admin/AdminDiagnostic"));
const NotFound = lazy(() => import("../pages/NotFound"));

const AppRoutes = () => {
  const { openAuthModal } = useAuthModal();

  // Enable global keyboard shortcuts
  useKeyboardNavigation({
    enableGlobalShortcuts: true,
    openAuthModal
  });

  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner />
      </div>
    }>
      <Routes>
        <Route path="/" element={<Index />} />
        {/* Keep /auth route as fallback for direct navigation */}
        <Route path="/auth" element={<Auth />} />
        <Route
          path="/dashboard"
          element={
            <ProtectedRoute requireProfile={true}>
              <ErrorBoundary>
                <Dashboard />
              </ErrorBoundary>
            </ProtectedRoute>
          }
        />
        <Route
          path="/profile"
          element={
            <ProtectedRoute requireProfile={true}>
              <ErrorBoundary>
                <Profile />
              </ErrorBoundary>
            </ProtectedRoute>
          }
        />
        <Route
          path="/profile-setup"
          element={
            <ProtectedRoute>
              <ErrorBoundary>
                <ProfileSetup />
              </ErrorBoundary>
            </ProtectedRoute>
          }
        />
        <Route
          path="/create-pledge"
          element={
            <ProtectedRoute requireProfile={true}>
              <ErrorBoundary>
                <CreatePledge />
              </ErrorBoundary>
            </ProtectedRoute>
          }
        />
        <Route
          path="/pledge/:pledgeId"
          element={
            <ErrorBoundary>
              <PledgeCard />
            </ErrorBoundary>
          }
        />
        <Route path="/demo-pledge" element={<DemoPledge />} />
        <Route path="/faq" element={<FAQ />} />
        <Route
          path="/admin"
          element={
            <AdminRoute>
              <ErrorBoundary>
                <AdminWelcome />
              </ErrorBoundary>
            </AdminRoute>
          }
        />
        <Route
          path="/admin/panel"
          element={
            <AdminRoute>
              <ErrorBoundary>
                <AdminPanel />
              </ErrorBoundary>
            </AdminRoute>
          }
        />
        <Route
          path="/admin-setup"
          element={
            <ProtectedRoute requireProfile={true}>
              <ErrorBoundary>
                <AdminSetupGuide />
              </ErrorBoundary>
            </ProtectedRoute>
          }
        />
        <Route
          path="/admin-diagnostic"
          element={
            <ProtectedRoute requireProfile={true}>
              <ErrorBoundary>
                <AdminDiagnostic />
              </ErrorBoundary>
            </ProtectedRoute>
          }
        />
        <Route path="*" element={<NotFound />} />
      </Routes>
    </Suspense>
  );
};

export default AppRoutes;
