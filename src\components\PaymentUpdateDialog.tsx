import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import type { Pledge, PaymentStatus } from "@/types/app";

interface PaymentUpdateDialogProps {
  pledge: Pledge | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (pledgeId: string, updates: { amount_paid: number; payment_status: PaymentStatus }) => void;
}

const PaymentUpdateDialog = ({ pledge, open, onOpenChange, onSave }: PaymentUpdateDialogProps) => {
  const [formData, setFormData] = useState<{
    amount_paid: number;
    payment_status: PaymentStatus;
  }>({
    amount_paid: 0,
    payment_status: 'pending' as PaymentStatus,
  });

  useEffect(() => {
    if (pledge) {
      setFormData({
        amount_paid: pledge.amount_paid,
        payment_status: pledge.payment_status as PaymentStatus,
      });
    }
  }, [pledge]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (pledge) {
      onSave(pledge.id, formData);
      onOpenChange(false);
    }
  };

  if (!pledge) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Update Payment</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="amount_paid">Amount Paid</Label>
            <Input
              id="amount_paid"
              type="number"
              value={formData.amount_paid}
              onChange={(e) => setFormData({ ...formData, amount_paid: Number(e.target.value) })}
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="payment_status">Payment Status</Label>
            <Select
              value={formData.payment_status}
              onValueChange={(value: PaymentStatus) => setFormData({ ...formData, payment_status: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="partial">Partial</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit">Save Changes</Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default PaymentUpdateDialog;
