import { UseFormReturn } from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { PhoneInput } from "@/components/ui/phone-input";
import { SignUpFormData } from "@/components/auth/schemas";

interface SignUpStep3Props {
  form: UseFormReturn<SignUpFormData>;
}

export function SignUpStep3({ form }: SignUpStep3Props) {
  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-semibold text-foreground">Additional Information</h2>
        <p className="text-muted-foreground mt-2">
          These details are optional but help create a better experience for your supporters
        </p>
      </div>

      <div className="space-y-4">
        <FormField
          control={form.control}
          name="treasurerPhone"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Phone Number (Optional)</FormLabel>
              <FormControl>
                <PhoneInput
                  value={field.value || ''}
                  onChange={(formatted, normalized) => field.onChange(normalized)}
                  showValidation={true}
                />
              </FormControl>
              <FormMessage />
              <p className="text-xs text-muted-foreground mt-1">
                Helps supporters contact you if needed
              </p>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="venue"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Wedding Venue (Optional)</FormLabel>
              <FormControl>
                <Input 
                  {...field} 
                  placeholder="e.g., Kampala Serena Hotel"
                  className="h-11"
                />
              </FormControl>
              <FormMessage />
              <p className="text-xs text-muted-foreground mt-1">
                Displayed on your pledge page to give context to supporters
              </p>
            </FormItem>
          )}
        />
      </div>

      <div className="bg-primary/5 border border-primary/20 p-4 rounded-lg">
        <div className="flex items-start gap-3">
          <div className="flex-shrink-0 w-2 h-2 bg-primary rounded-full mt-2"></div>
          <div>
            <p className="text-sm font-medium text-foreground mb-1">
              Ready to create your account!
            </p>
            <p className="text-xs text-muted-foreground">
              You can always update these details later in your profile settings.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
