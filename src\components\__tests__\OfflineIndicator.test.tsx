import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, act } from '@testing-library/react';
import OfflineIndicator from '../OfflineIndicator';

// Mock the useOffline hook
vi.mock('@/hooks/use-offline', () => ({
  useOffline: vi.fn(),
}));

import { useOffline } from '@/hooks/use-offline';

describe('OfflineIndicator Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('renders nothing when online and never was offline', () => {
    (useOffline as any).mockReturnValue({
      isOffline: false,
      wasOffline: false,
      isOnline: true,
    });

    const { container } = render(<OfflineIndicator />);
    expect(container.firstChild).toBeNull();
  });

  it('shows offline indicator when offline', () => {
    (useOffline as any).mockReturnValue({
      isOffline: true,
      wasOffline: false,
      isOnline: false,
    });

    render(<OfflineIndicator />);

    expect(screen.getByText(/You're currently offline/)).toBeInTheDocument();
    expect(screen.getByText(/Some features may not be available/)).toBeInTheDocument();

    // Check for WiFi off icon (SVG)
    const wifiOffIcon = screen.getByRole('alert').querySelector('svg');
    expect(wifiOffIcon).toBeInTheDocument();
    expect(wifiOffIcon).toHaveClass('lucide-wifi-off');
  });

  it('shows connection restored indicator when back online after being offline', () => {
    (useOffline as any).mockReturnValue({
      isOffline: false,
      wasOffline: true,
      isOnline: true,
    });

    render(<OfflineIndicator />);

    expect(screen.getByText(/Connection restored!/)).toBeInTheDocument();
    expect(screen.getByText(/You're back online/)).toBeInTheDocument();

    // Check for WiFi on icon (SVG)
    const wifiOnIcon = screen.getByRole('alert').querySelector('svg');
    expect(wifiOnIcon).toBeInTheDocument();
    expect(wifiOnIcon).toHaveClass('lucide-wifi');
  });

  it('has correct styling for offline state', () => {
    (useOffline as any).mockReturnValue({
      isOffline: true,
      wasOffline: false,
      isOnline: false,
    });

    render(<OfflineIndicator />);

    const alert = screen.getByRole('alert');
    expect(alert).toHaveClass('fixed', 'top-4', 'left-4', 'right-4', 'z-50', 'bg-red-50', 'border-red-200');
  });

  it('has correct styling for connection restored state', () => {
    (useOffline as any).mockReturnValue({
      isOffline: false,
      wasOffline: true,
      isOnline: true,
    });

    render(<OfflineIndicator />);

    const alert = screen.getByRole('alert');
    expect(alert).toHaveClass('fixed', 'top-4', 'left-4', 'right-4', 'z-50', 'bg-green-50', 'border-green-200');
  });

  it('transitions correctly from offline to online', () => {
    // Start offline
    (useOffline as any).mockReturnValue({
      isOffline: true,
      wasOffline: false,
      isOnline: false,
    });

    const { rerender } = render(<OfflineIndicator />);
    expect(screen.getByText(/You're currently offline/)).toBeInTheDocument();

    // Go back online
    (useOffline as any).mockReturnValue({
      isOffline: false,
      wasOffline: true,
      isOnline: true,
    });

    rerender(<OfflineIndicator />);
    expect(screen.getByText(/Connection restored!/)).toBeInTheDocument();
    expect(screen.queryByText(/You're currently offline/)).not.toBeInTheDocument();
  });

  it('handles multiple offline/online cycles', () => {
    // Start online
    (useOffline as any).mockReturnValue({
      isOffline: false,
      wasOffline: false,
      isOnline: true,
    });

    const { rerender } = render(<OfflineIndicator />);
    expect(screen.queryByRole('alert')).not.toBeInTheDocument();

    // Go offline
    (useOffline as any).mockReturnValue({
      isOffline: true,
      wasOffline: false,
      isOnline: false,
    });

    rerender(<OfflineIndicator />);
    expect(screen.getByText(/You're currently offline/)).toBeInTheDocument();

    // Go back online
    (useOffline as any).mockReturnValue({
      isOffline: false,
      wasOffline: true,
      isOnline: true,
    });

    rerender(<OfflineIndicator />);
    expect(screen.getByText(/Connection restored!/)).toBeInTheDocument();

    // Stay online (should show nothing after restoration message)
    (useOffline as any).mockReturnValue({
      isOffline: false,
      wasOffline: false,
      isOnline: true,
    });

    rerender(<OfflineIndicator />);
    expect(screen.queryByRole('alert')).not.toBeInTheDocument();
  });
});
