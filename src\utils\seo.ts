// SEO and Social Media Meta Tag Utilities
import React from 'react';

export interface SEOData {
  title: string;
  description: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'profile';
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
}

export const defaultSEO: SEOData = {
  title: "PledgeForLove Uganda | Digital Wedding Contribution Platform",
  description: "Modern digital solution for Ugandan wedding contributions. Create beautiful online pledge cards, track payments, and simplify your wedding planning in Uganda.",
  keywords: "Uganda wedding, wedding contributions, pledge cards, wedding gifts, Kampala weddings, African weddings, digital pledge, wedding planning",
  image: "https://pledgeforlove.ug/images/love.jpg",
  url: "https://pledgeforlove.ug",
  type: "website",
  author: "PledgeForLove Uganda"
};

export const pageSEO: Record<string, SEOData> = {
  home: {
    title: "PledgeForLove Uganda | Digital Wedding Contribution Platform",
    description: "Transform traditional wedding contributions with our beautiful digital pledge cards for Ugandan weddings. Modern, secure, and easy to use.",
    keywords: "Uganda wedding, digital pledge cards, wedding contributions, online wedding registry, Kampala weddings",
    type: "website"
  },
  auth: {
    title: "Sign In | PledgeForLove Uganda",
    description: "Access your PledgeForLove account to manage wedding contributions and pledge cards. Secure login for Ugandan wedding planning platform.",
    keywords: "login, sign in, wedding account, pledge management, Uganda wedding platform",
    type: "website"
  },
  dashboard: {
    title: "Dashboard | PledgeForLove Uganda",
    description: "Manage your wedding pledges, track contributions, and view payment status. Complete dashboard for your Ugandan wedding planning.",
    keywords: "wedding dashboard, pledge management, contribution tracking, wedding planning Uganda",
    type: "website"
  },
  "create-pledge": {
    title: "Create Pledge | PledgeForLove Uganda",
    description: "Create beautiful digital pledge cards for your Ugandan wedding. Easy-to-use form for setting up wedding contributions and guest pledges.",
    keywords: "create pledge, wedding pledge card, digital wedding invitation, Uganda wedding contributions",
    type: "website"
  },
  profile: {
    title: "Profile | PledgeForLove Uganda",
    description: "Manage your PledgeForLove profile settings, wedding details, and account preferences. Personalize your Ugandan wedding experience.",
    keywords: "user profile, wedding settings, account management, Uganda wedding profile",
    type: "profile"
  },
  "profile-setup": {
    title: "Complete Your Profile | PledgeForLove Uganda",
    description: "Set up your wedding profile with details, customize your pledge card theme, and configure privacy settings. Complete your PledgeForLove account setup.",
    keywords: "profile setup, wedding profile, account setup, pledge card customization, Uganda wedding setup",
    type: "website"
  }
};

// Function to update document meta tags dynamically
export const updateMetaTags = (seoData: Partial<SEOData>) => {
  const data = { ...defaultSEO, ...seoData };
  
  // Update title
  document.title = data.title;
  
  // Update or create meta tags
  const updateMetaTag = (name: string, content: string, property = false) => {
    const selector = property ? `meta[property="${name}"]` : `meta[name="${name}"]`;
    let meta = document.querySelector(selector) as HTMLMetaElement;
    
    if (!meta) {
      meta = document.createElement('meta');
      if (property) {
        meta.setAttribute('property', name);
      } else {
        meta.setAttribute('name', name);
      }
      document.head.appendChild(meta);
    }
    
    meta.setAttribute('content', content);
  };
  
  // Basic meta tags
  updateMetaTag('description', data.description);
  if (data.keywords) updateMetaTag('keywords', data.keywords);
  if (data.author) updateMetaTag('author', data.author);
  
  // Open Graph tags
  updateMetaTag('og:title', data.title, true);
  updateMetaTag('og:description', data.description, true);
  updateMetaTag('og:type', data.type || 'website', true);
  if (data.url) updateMetaTag('og:url', data.url, true);
  if (data.image) {
    updateMetaTag('og:image', data.image, true);
    updateMetaTag('og:image:alt', `${data.title} - Preview Image`, true);
  }
  
  // Twitter Card tags
  updateMetaTag('twitter:title', data.title);
  updateMetaTag('twitter:description', data.description);
  if (data.image) {
    updateMetaTag('twitter:image', data.image);
    updateMetaTag('twitter:image:alt', `${data.title} - Preview Image`);
  }
  if (data.url) updateMetaTag('twitter:url', data.url);
  
  // Article specific tags
  if (data.type === 'article') {
    if (data.publishedTime) updateMetaTag('article:published_time', data.publishedTime, true);
    if (data.modifiedTime) updateMetaTag('article:modified_time', data.modifiedTime, true);
    if (data.author) updateMetaTag('article:author', data.author, true);
  }
};

// Hook for React components to update SEO
export const useSEO = (pageKey: string, customData?: Partial<SEOData>) => {
  const seoData = pageSEO[pageKey] || defaultSEO;
  const finalData = { ...seoData, ...customData };
  
  // Update meta tags when component mounts or data changes
  React.useEffect(() => {
    updateMetaTags(finalData);
    
    // Cleanup function to reset to default when component unmounts
    return () => {
      if (pageKey !== 'home') {
        updateMetaTags(defaultSEO);
      }
    };
  }, [pageKey, customData]);
};

// Generate structured data for rich snippets
export const generateStructuredData = (type: 'Organization' | 'WebSite' | 'Event' | 'Service' | 'SoftwareApplication' | 'FAQPage', data: any = {}) => {
  const baseStructure = {
    "@context": "https://schema.org",
    "@type": type
  };
  
  switch (type) {
    case 'Organization':
      return {
        ...baseStructure,
        name: "PledgeForLove Uganda",
        url: "https://pledgeforlove.ug",
        logo: "https://pledgeforlove.ug/images/logo.png",
        description: "Digital wedding contribution platform for Ugandan weddings",
        address: {
          "@type": "PostalAddress",
          addressCountry: "UG",
          addressLocality: "Kampala"
        },
        contactPoint: {
          "@type": "ContactPoint",
          contactType: "customer service",
          email: "<EMAIL>"
        },
        sameAs: [
          "https://twitter.com/PledgeForLoveUG",
          "https://facebook.com/PledgeForLoveUG"
        ],
        ...data
      };
      
    case 'WebSite':
      return {
        ...baseStructure,
        name: "PledgeForLove Uganda",
        url: "https://pledgeforlove.ug",
        description: "Digital wedding contribution platform for Ugandan weddings",
        publisher: {
          "@type": "Organization",
          name: "PledgeForLove Uganda"
        },
        potentialAction: {
          "@type": "SearchAction",
          target: "https://pledgeforlove.ug/search?q={search_term_string}",
          "query-input": "required name=search_term_string"
        },
        ...data
      };
      
    case 'Event':
      return {
        ...baseStructure,
        name: data.name || "Wedding Event",
        description: data.description || "Wedding celebration with digital pledge contributions",
        startDate: data.startDate,
        endDate: data.endDate,
        location: {
          "@type": "Place",
          name: data.location || "Uganda",
          address: {
            "@type": "PostalAddress",
            addressCountry: "UG"
          }
        },
        organizer: {
          "@type": "Person",
          name: data.organizer || "Wedding Couple"
        },
        ...data
      };

    case 'Service':
      return {
        ...baseStructure,
        name: "Digital Wedding Contribution Platform",
        description: "Professional digital platform for managing wedding contributions and pledge cards in Uganda",
        provider: {
          "@type": "Organization",
          name: "PledgeForLove Uganda"
        },
        areaServed: {
          "@type": "Country",
          name: "Uganda"
        },
        serviceType: "Wedding Planning Service",
        category: "Wedding Services",
        offers: {
          "@type": "Offer",
          description: "Digital wedding contribution management",
          price: "0",
          priceCurrency: "UGX"
        },
        ...data
      };

    case 'SoftwareApplication':
      return {
        ...baseStructure,
        name: "PledgeForLove Uganda",
        description: "Digital wedding contribution platform for creating and managing pledge cards",
        applicationCategory: "WebApplication",
        operatingSystem: "Web Browser",
        offers: {
          "@type": "Offer",
          price: "0",
          priceCurrency: "UGX"
        },
        author: {
          "@type": "Organization",
          name: "PledgeForLove Uganda"
        },
        aggregateRating: {
          "@type": "AggregateRating",
          ratingValue: "4.8",
          ratingCount: "150",
          bestRating: "5",
          worstRating: "1"
        },
        ...data
      };

    case 'FAQPage':
      return {
        ...baseStructure,
        mainEntity: data.questions || [
          {
            "@type": "Question",
            name: "How does PledgeForLove work?",
            acceptedAnswer: {
              "@type": "Answer",
              text: "PledgeForLove allows couples to create digital pledge cards for their wedding, making it easy for guests to contribute and track payments online."
            }
          },
          {
            "@type": "Question",
            name: "Is PledgeForLove free to use?",
            acceptedAnswer: {
              "@type": "Answer",
              text: "Yes, PledgeForLove is completely free to use for creating and managing wedding pledge cards."
            }
          },
          {
            "@type": "Question",
            name: "Can I use PledgeForLove for weddings in Uganda?",
            acceptedAnswer: {
              "@type": "Answer",
              text: "Absolutely! PledgeForLove is specifically designed for Ugandan weddings and supports local payment methods and traditions."
            }
          }
        ],
        ...data
      };

    default:
      return baseStructure;
  }
};

// Function to inject structured data into the page
export const injectStructuredData = (structuredData: any) => {
  const script = document.createElement('script');
  script.type = 'application/ld+json';
  script.textContent = JSON.stringify(structuredData);
  
  // Remove existing structured data script if present
  const existingScript = document.querySelector('script[type="application/ld+json"]');
  if (existingScript) {
    existingScript.remove();
  }
  
  document.head.appendChild(script);
};
