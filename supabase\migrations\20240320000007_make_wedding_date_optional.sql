-- Make wedding_date optional to support progressive signup flow
-- This resolves the schema-UI inconsistency where wedding_date was required in schema
-- but labeled as optional in the signup form

-- Alter wedding_date column to allow NULL values
ALTER TABLE profiles 
ALTER COLUMN wedding_date DROP NOT NULL;

-- Add comment to document the change
COMMENT ON COLUMN profiles.wedding_date IS 'Wedding date - optional during signup, can be completed later in profile';
