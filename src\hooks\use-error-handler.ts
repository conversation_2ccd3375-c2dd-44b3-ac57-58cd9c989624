import { useToast } from "@/hooks/use-toast";
import { 
  isAuthError, 
  isValidationError, 
  isDatabaseError, 
  handleSupabaseError 
} from "@/types/app";

interface ErrorHandlerOptions {
  showToast?: boolean;
  defaultMessage?: string;
  onError?: (error: unknown) => void;
}

export const useErrorHandler = () => {
  const { toast } = useToast();

  const handleError = (
    error: unknown, 
    options: ErrorHandlerOptions = {}
  ) => {
    const {
      showToast = true,
      defaultMessage = "An unexpected error occurred. Please try again.",
      onError
    } = options;

    console.error('Error occurred:', error);

    // Call custom error handler if provided
    if (onError) {
      onError(error);
    }

    if (!showToast) return;

    // Handle different error types with consistent messaging
    if (isAuthError(error)) {
      toast({
        title: "Authentication Error",
        description: error.message,
        variant: "destructive",
      });
    } else if (isValidationError(error)) {
      toast({
        title: "Validation Error", 
        description: error.message,
        variant: "destructive",
      });
    } else if (isDatabaseError(error)) {
      toast({
        title: "Database Error",
        description: error.message,
        variant: "destructive",
      });
    } else if (error instanceof Error) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    } else {
      toast({
        title: "Error",
        description: defaultMessage,
        variant: "destructive",
      });
    }
  };

  const handleAsyncError = async (
    asyncOperation: () => Promise<void>,
    options: ErrorHandlerOptions = {}
  ) => {
    try {
      await asyncOperation();
    } catch (error) {
      handleError(error, options);
    }
  };

  const handleSupabaseOperation = async (
    operation: () => Promise<void>,
    options: ErrorHandlerOptions = {}
  ) => {
    try {
      await operation();
    } catch (error) {
      // Use the existing handleSupabaseError for consistent error transformation
      try {
        handleSupabaseError(error);
      } catch (transformedError) {
        handleError(transformedError, options);
      }
    }
  };

  return {
    handleError,
    handleAsyncError,
    handleSupabaseOperation
  };
};
