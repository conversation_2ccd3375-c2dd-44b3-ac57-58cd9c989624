# Phone Number Validation System

## Overview

The Love Pledge Uganda application includes a comprehensive phone number validation system designed specifically for Ugandan phone numbers. This system provides normalization, validation, formatting, and user-friendly input components.

## Features

- **Multiple Format Support**: Accepts various Ugandan phone number formats
- **Real-time Validation**: Provides immediate feedback as users type
- **Auto-formatting**: Formats phone numbers with appropriate spacing
- **Normalization**: Converts all valid formats to a consistent `+256XXXXXXXXX` format
- **User-friendly Error Messages**: Provides helpful suggestions for corrections

## Supported Formats

The system accepts the following Ugandan phone number formats:

### Input Formats
- `+256 700 123 456` (International with spaces)
- `+256-700-123-456` (International with dashes)
- `+256700123456` (International without spaces)
- `256700123456` (Without + prefix)
- `0700 123 456` (Local with spaces)
- `0700123456` (Local without spaces)
- `700123456` (9-digit format)

### Output Format
All valid phone numbers are normalized to: `+256XXXXXXXXX`

## Validation Rules

### Valid Prefixes
Ugandan mobile numbers must start with digits 2-9 after the country code (+256):
- **MTN**: 70x, 71x, 72x, 73x, 74x, 75x, 76x, 77x, 78x, 79x
- **Airtel**: 20x, 25x, 26x
- **Africell**: 31x, 32x, 39x
- **Other operators**: Various 2-9 prefixes

### Invalid Patterns
- Numbers starting with 0 or 1 after +256
- Numbers shorter than 9 digits (after country code)
- Numbers longer than 9 digits (after country code)
- Non-Ugandan country codes
- Non-numeric characters (except +, spaces, dashes, parentheses)

## Components

### Core Functions

#### `normalizePhoneNumber(phone: string): string`
Converts various phone number formats to the standard `+256XXXXXXXXX` format.

```typescript
import { normalizePhoneNumber } from '@/types/app';

// Examples
normalizePhoneNumber('0700123456');     // '+256700123456'
normalizePhoneNumber('256700123456');   // '+256700123456'
normalizePhoneNumber('+256 700 123 456'); // '+256700123456'
```

#### `validatePhoneNumber(phone: string): ValidationResult`
Validates a phone number and returns detailed validation results.

```typescript
import { validatePhoneNumber } from '@/types/app';

const result = validatePhoneNumber('0700123456');
// {
//   isValid: true,
//   errors: [],
//   normalizedValue: '+256700123456'
// }
```

### UI Components

#### `PhoneInput`
A specialized input component with real-time formatting and validation.

```tsx
import { PhoneInput } from '@/components/ui/phone-input';

<PhoneInput
  value={phoneNumber}
  onChange={(formatted, normalized) => {
    setDisplayValue(formatted);    // For UI display
    setStoredValue(normalized);    // For database storage
  }}
  showValidation={true}
/>
```

#### Props
- `value`: Current phone number value
- `onChange`: Callback with formatted and normalized values
- `showValidation`: Whether to show real-time validation feedback
- All standard HTML input props are supported

### Formatting Utilities

#### `formatPhoneInput(value: string, previousValue?: string): string`
Formats phone numbers as users type, with intelligent spacing.

#### `validatePhoneInput(value: string): ValidationFeedback`
Provides real-time validation feedback for incomplete inputs.

## Usage Examples

### Basic Form Integration

```tsx
import { useState } from 'react';
import { PhoneInput } from '@/components/ui/phone-input';
import { validatePhoneNumber } from '@/types/app';

function ContactForm() {
  const [phone, setPhone] = useState('');
  const [normalizedPhone, setNormalizedPhone] = useState('');

  const handleSubmit = () => {
    const validation = validatePhoneNumber(normalizedPhone);
    if (validation.isValid) {
      // Submit with validation.normalizedValue
      submitForm({ phone: validation.normalizedValue });
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <PhoneInput
        value={phone}
        onChange={(formatted, normalized) => {
          setPhone(formatted);
          setNormalizedPhone(normalized);
        }}
        showValidation={true}
      />
    </form>
  );
}
```

### Manual Validation

```typescript
import { validatePhoneNumber, normalizePhoneNumber } from '@/types/app';

function processPhoneNumber(input: string) {
  // Normalize first
  const normalized = normalizePhoneNumber(input);
  
  // Then validate
  const validation = validatePhoneNumber(normalized);
  
  if (validation.isValid) {
    // Store the normalized value
    return validation.normalizedValue;
  } else {
    // Handle validation errors
    console.error('Invalid phone number:', validation.errors);
    return null;
  }
}
```

## Error Messages

The system provides user-friendly error messages:

- **Empty input**: Allowed (optional fields)
- **Invalid format**: "Please enter a valid Ugandan phone number (e.g., +256 700 123 456, 0700 123 456, or 700123456)"
- **Invalid prefix**: "Please enter a valid Ugandan mobile number (should start with 2-9 after +256)"
- **Missing + prefix**: "Missing + prefix" with suggestion
- **Incomplete number**: "Need X more digits"
- **Too many digits**: "Too many digits"

## Testing

Comprehensive test suite covers:
- Normalization of all supported formats
- Validation of valid and invalid numbers
- Input formatting behavior
- Real-time validation feedback
- Edge cases and error conditions

Run tests with:
```bash
npm test -- phoneValidation.test.ts
```

## Database Storage

Always store the normalized phone number format (`+256XXXXXXXXX`) in the database:

```sql
-- Example table structure
CREATE TABLE users (
  id UUID PRIMARY KEY,
  name TEXT NOT NULL,
  phone TEXT CHECK (phone ~ '^\+256[2-9][0-9]{8}$' OR phone IS NULL)
);
```

## Migration Notes

When migrating existing phone data:
1. Use `normalizePhoneNumber()` to convert existing formats
2. Validate with `validatePhoneNumber()` to identify invalid entries
3. Update database with normalized values
4. Handle any validation errors appropriately

## Best Practices

1. **Always normalize before storage**: Use the `normalizedValue` from validation results
2. **Validate on both client and server**: Client validation for UX, server validation for security
3. **Handle optional fields**: Empty phone numbers should be allowed in most cases
4. **Provide clear feedback**: Use the built-in validation messages and suggestions
5. **Test thoroughly**: Verify with various input formats and edge cases
