import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

interface KeyboardNavigationOptions {
  enableGlobalShortcuts?: boolean;
  onEscape?: () => void;
  openAuthModal?: (mode?: 'signin' | 'signup') => void;
}

export const useKeyboardNavigation = (options: KeyboardNavigationOptions = {}) => {
  const navigate = useNavigate();
  const { enableGlobalShortcuts = false, onEscape, openAuthModal } = options;

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Handle Escape key
      if (event.key === 'Escape' && onEscape) {
        onEscape();
        return;
      }

      // Global shortcuts (only when enabled and no input is focused)
      if (enableGlobalShortcuts && !isInputFocused()) {
        // Alt + H: Home
        if (event.altKey && event.key === 'h') {
          event.preventDefault();
          navigate('/');
          return;
        }

        // Alt + D: Dashboard
        if (event.altKey && event.key === 'd') {
          event.preventDefault();
          navigate('/dashboard');
          return;
        }

        // Alt + P: Profile
        if (event.altKey && event.key === 'p') {
          event.preventDefault();
          navigate('/profile');
          return;
        }

        // Alt + C: Create Pledge
        if (event.altKey && event.key === 'c') {
          event.preventDefault();
          navigate('/create-pledge');
          return;
        }

        // Alt + A: Auth
        if (event.altKey && event.key === 'a') {
          event.preventDefault();
          if (openAuthModal) {
            openAuthModal('signin');
          } else {
            navigate('/auth');
          }
          return;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [navigate, enableGlobalShortcuts, onEscape]);

  return {
    // Helper function to check if an input element is focused
    isInputFocused: () => isInputFocused(),
  };
};

// Helper function to check if an input element is currently focused
const isInputFocused = (): boolean => {
  const activeElement = document.activeElement;
  if (!activeElement) return false;

  const inputTypes = ['input', 'textarea', 'select'];
  const tagName = activeElement.tagName.toLowerCase();
  
  return inputTypes.includes(tagName) || 
         activeElement.getAttribute('contenteditable') === 'true' ||
         activeElement.getAttribute('role') === 'textbox';
};

// Hook for focus management in modals/dialogs
export const useFocusManagement = (isOpen: boolean) => {
  useEffect(() => {
    if (!isOpen) return;

    // Store the element that was focused before opening the modal
    const previouslyFocusedElement = document.activeElement as HTMLElement;

    // Focus the first focusable element in the modal
    const focusableElements = document.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    if (focusableElements.length > 0) {
      (focusableElements[0] as HTMLElement).focus();
    }

    // Return focus to the previously focused element when modal closes
    return () => {
      if (previouslyFocusedElement) {
        previouslyFocusedElement.focus();
      }
    };
  }, [isOpen]);
};

// Hook for managing focus trap in modals
export const useFocusTrap = (isOpen: boolean, containerRef: React.RefObject<HTMLElement>) => {
  useEffect(() => {
    if (!isOpen || !containerRef.current) return;

    const container = containerRef.current;
    
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key !== 'Tab') return;

      const focusableElements = container.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );
      
      const firstElement = focusableElements[0] as HTMLElement;
      const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

      if (event.shiftKey) {
        // Shift + Tab
        if (document.activeElement === firstElement) {
          event.preventDefault();
          lastElement.focus();
        }
      } else {
        // Tab
        if (document.activeElement === lastElement) {
          event.preventDefault();
          firstElement.focus();
        }
      }
    };

    container.addEventListener('keydown', handleKeyDown);
    return () => container.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, containerRef]);
};
