-- Verify and fix missing columns in profiles table
-- This migration ensures theme and special_message columns exist

-- Add theme column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'profiles' 
        AND column_name = 'theme'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.profiles 
        ADD COLUMN theme TEXT DEFAULT 'sunset';
        
        RAISE NOTICE 'Added theme column to profiles table';
    ELSE
        RAISE NOTICE 'Theme column already exists in profiles table';
    END IF;
END $$;

-- Add special_message column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'profiles' 
        AND column_name = 'special_message'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.profiles 
        ADD COLUMN special_message TEXT DEFAULT 'Join us in celebrating our special day with your loving support and contributions.';
        
        RAISE NOTICE 'Added special_message column to profiles table';
    ELSE
        RAISE NOTICE 'Special_message column already exists in profiles table';
    END IF;
END $$;

-- Add is_public column if it doesn't exist (from TypeScript types)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'profiles' 
        AND column_name = 'is_public'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.profiles 
        ADD COLUMN is_public BOOLEAN DEFAULT false;
        
        RAISE NOTICE 'Added is_public column to profiles table';
    ELSE
        RAISE NOTICE 'Is_public column already exists in profiles table';
    END IF;
END $$;

-- Verify all expected columns exist
DO $$
DECLARE
    missing_columns TEXT[] := ARRAY[]::TEXT[];
    expected_columns TEXT[] := ARRAY[
        'id', 'bride_name', 'groom_name', 'wedding_date', 'created_at', 'updated_at',
        'treasurer_name', 'treasurer_phone', 'venue', 'email', 'theme', 'special_message', 'is_public'
    ];
    col TEXT;
BEGIN
    FOREACH col IN ARRAY expected_columns
    LOOP
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'profiles' 
            AND column_name = col
            AND table_schema = 'public'
        ) THEN
            missing_columns := array_append(missing_columns, col);
        END IF;
    END LOOP;
    
    IF array_length(missing_columns, 1) > 0 THEN
        RAISE WARNING 'Missing columns in profiles table: %', array_to_string(missing_columns, ', ');
    ELSE
        RAISE NOTICE 'All expected columns exist in profiles table';
    END IF;
END $$;
