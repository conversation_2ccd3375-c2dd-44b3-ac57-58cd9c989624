import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import type { Pledge } from "@/types/app";

interface EditPledgeDialogProps {
  pledge: Pledge | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (pledgeId: string, updates: Partial<Pledge>) => void;
}

const EditPledgeDialog = ({ pledge, open, onOpenChange, onSave }: EditPledgeDialogProps) => {
  const [formData, setFormData] = useState<Partial<Pledge>>({});

  useEffect(() => {
    if (pledge) {
      setFormData({
        guest_name: pledge.guest_name,
        guest_phone: pledge.guest_phone,
        amount_pledged: pledge.amount_pledged,
        notes: pledge.notes,
      });
    }
  }, [pledge]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (pledge) {
      onSave(pledge.id, formData);
      onOpenChange(false);
    }
  };

  if (!pledge) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Edit Pledge</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="guest_name">Guest Name</Label>
            <Input
              id="guest_name"
              value={formData.guest_name || ''}
              onChange={(e) => setFormData({ ...formData, guest_name: e.target.value })}
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="guest_phone">Phone Number</Label>
            <Input
              id="guest_phone"
              value={formData.guest_phone || ''}
              onChange={(e) => setFormData({ ...formData, guest_phone: e.target.value })}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="amount_pledged">Amount Pledged</Label>
            <Input
              id="amount_pledged"
              type="number"
              value={formData.amount_pledged || ''}
              onChange={(e) => setFormData({ ...formData, amount_pledged: Number(e.target.value) })}
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={formData.notes || ''}
              onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
            />
          </div>
          <div className="flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit">Save Changes</Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default EditPledgeDialog;
