import { cn } from "@/lib/utils";

interface LoadingSpinnerProps {
  size?: "sm" | "md" | "lg";
  className?: string;
  text?: string;
  variant?: "default" | "card" | "overlay";
}

const LoadingSpinner = ({ 
  size = "md", 
  className, 
  text = "Loading...", 
  variant = "default" 
}: LoadingSpinnerProps) => {
  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-8 w-8", 
    lg: "h-12 w-12"
  };

  const textSizeClasses = {
    sm: "text-sm",
    md: "text-base",
    lg: "text-lg"
  };

  const spinner = (
    <div
      className={cn(
        "animate-spin rounded-full border-2 border-gray-300 border-t-blue-600",
        sizeClasses[size],
        className
      )}
    />
  );

  if (variant === "card") {
    return (
      <div className="flex flex-col items-center justify-center p-8 space-y-4">
        {spinner}
        <p className={cn("text-gray-600", textSizeClasses[size])}>{text}</p>
      </div>
    );
  }

  if (variant === "overlay") {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 flex flex-col items-center space-y-4">
          {spinner}
          <p className={cn("text-gray-600", textSizeClasses[size])}>{text}</p>
        </div>
      </div>
    );
  }

  // Default variant - inline spinner with text
  return (
    <div className="flex items-center justify-center space-x-2">
      {spinner}
      <span className={cn("text-gray-600", textSizeClasses[size])}>{text}</span>
    </div>
  );
};

export default LoadingSpinner;
