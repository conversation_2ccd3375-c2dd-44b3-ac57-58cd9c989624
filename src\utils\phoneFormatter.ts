/**
 * Phone number formatting utilities for Ugandan phone numbers
 */

/**
 * Formats a phone number as the user types for better UX
 * @param value - The current input value
 * @param previousValue - The previous input value (for backspace detection)
 * @returns Formatted phone number string
 */
export const formatPhoneInput = (value: string, previousValue?: string): string => {
  // Remove all non-digit characters except +
  const cleaned = value.replace(/[^\d+]/g, '');
  
  // Handle backspace - if the new value is shorter, don't auto-format
  const isBackspace = previousValue && value.length < previousValue.length;
  
  // If user starts typing without +256, auto-add it
  if (cleaned.length > 0 && !cleaned.startsWith('+256') && !cleaned.startsWith('0') && !isBackspace) {
    if (cleaned.startsWith('+')) {
      // User typed + but not +256, guide them
      if (cleaned.length === 1) return '+256 ';
      if (cleaned.length <= 4 && !cleaned.startsWith('+256')) {
        return '+256 ';
      }
    } else {
      // User started typing digits, assume they want +256
      return '+256 ' + cleaned;
    }
  }
  
  // Format +256 numbers with spaces
  if (cleaned.startsWith('+256')) {
    const digits = cleaned.substring(4);
    if (digits.length === 0) return '+256 ';
    if (digits.length <= 3) return `+256 ${digits}`;
    if (digits.length <= 6) return `+256 ${digits.substring(0, 3)} ${digits.substring(3)}`;
    if (digits.length <= 9) return `+256 ${digits.substring(0, 3)} ${digits.substring(3, 6)} ${digits.substring(6)}`;
    // Limit to 9 digits after +256
    return `+256 ${digits.substring(0, 3)} ${digits.substring(3, 6)} ${digits.substring(6, 9)}`;
  }
  
  // Format 0xxx numbers with spaces
  if (cleaned.startsWith('0')) {
    if (cleaned.length <= 4) return cleaned;
    if (cleaned.length <= 7) return `${cleaned.substring(0, 4)} ${cleaned.substring(4)}`;
    if (cleaned.length <= 10) return `${cleaned.substring(0, 4)} ${cleaned.substring(4, 7)} ${cleaned.substring(7)}`;
    // Limit to 10 digits total for 0xxx format
    return `${cleaned.substring(0, 4)} ${cleaned.substring(4, 7)} ${cleaned.substring(7, 10)}`;
  }
  
  return cleaned;
};

/**
 * Gets placeholder text for phone input based on current value
 * @param value - Current input value
 * @returns Appropriate placeholder text
 */
export const getPhonePlaceholder = (value: string): string => {
  if (!value || value.length === 0) {
    return '+256 700 123 456';
  }
  
  if (value.startsWith('+256')) {
    return '+256 700 123 456';
  }
  
  if (value.startsWith('0')) {
    return '0700 123 456';
  }
  
  return '+256 700 123 456';
};

/**
 * Validates phone input in real-time and provides helpful feedback
 * @param value - Current input value
 * @returns Validation feedback object
 */
export const validatePhoneInput = (value: string): {
  isValid: boolean;
  message?: string;
  suggestion?: string;
} => {
  if (!value.trim()) {
    return { isValid: true };
  }
  
  const cleaned = value.replace(/[\s\-\(\)]/g, '');
  
  // Check for common mistakes
  if (cleaned.startsWith('256') && !cleaned.startsWith('+256')) {
    return {
      isValid: false,
      message: 'Missing + prefix',
      suggestion: 'Try: +' + cleaned
    };
  }
  
  if (cleaned.startsWith('+256')) {
    const digits = cleaned.substring(4);
    if (digits.length < 9) {
      return {
        isValid: false,
        message: `Need ${9 - digits.length} more digit${9 - digits.length === 1 ? '' : 's'}`,
      };
    }
    if (digits.length > 9) {
      return {
        isValid: false,
        message: 'Too many digits after +256',
      };
    }
    return { isValid: true };
  }
  
  if (cleaned.startsWith('0')) {
    if (cleaned.length < 10) {
      return {
        isValid: false,
        message: `Need ${10 - cleaned.length} more digit${10 - cleaned.length === 1 ? '' : 's'}`,
      };
    }
    if (cleaned.length > 10) {
      return {
        isValid: false,
        message: 'Too many digits',
      };
    }
    return { isValid: true };
  }
  
  // 9-digit number without prefix
  if (/^[0-9]{9}$/.test(cleaned)) {
    return { isValid: true };
  }
  
  if (/^[0-9]+$/.test(cleaned)) {
    if (cleaned.length < 9) {
      return {
        isValid: false,
        message: `Need ${9 - cleaned.length} more digit${9 - cleaned.length === 1 ? '' : 's'}`,
      };
    }
    if (cleaned.length > 9) {
      return {
        isValid: false,
        message: 'Too many digits',
      };
    }
  }
  
  return {
    isValid: false,
    message: 'Invalid phone number format',
    suggestion: 'Use format: +256 700 123 456 or 0700 123 456'
  };
};
