import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form } from "@/components/ui/form";
import { MultiStepForm } from "@/components/ui/multi-step-form";
import { Button } from "@/components/ui/button";
import { AlertCircle, RotateCcw } from "lucide-react";
import { SignUpStep1 } from "./signup-steps/SignUpStep1";
import { SignUpStep2 } from "./signup-steps/SignUpStep2";
import { SignUpStep3 } from "./signup-steps/SignUpStep3";
import { SignUpFormData, signUpSchema } from "@/components/auth/schemas";
import { z } from "zod";

interface MultiStepSignUpFormProps {
  onSubmit: (data: SignUpFormData) => Promise<void>;
  loading?: boolean;
}

// Step-specific validation schemas
const step1Schema = signUpSchema.pick({
  email: true,
  password: true,
  treasurerName: true,
});

const step2Schema = signUpSchema.pick({
  brideName: true,
  groomName: true,
  weddingDate: true,
});

const step3Schema = signUpSchema.pick({
  treasurerPhone: true,
  venue: true,
});

const FORM_STORAGE_KEY = "signup-form-data";
const STEP_STORAGE_KEY = "signup-current-step";

export function MultiStepSignUpForm({ onSubmit, loading = false }: MultiStepSignUpFormProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hasRestoredData, setHasRestoredData] = useState(false);

  // Load persisted data from localStorage
  const getPersistedData = (): Partial<SignUpFormData> => {
    try {
      const stored = localStorage.getItem(FORM_STORAGE_KEY);
      return stored ? JSON.parse(stored) : {};
    } catch {
      return {};
    }
  };

  const getPersistedStep = (): number => {
    try {
      const stored = localStorage.getItem(STEP_STORAGE_KEY);
      return stored ? parseInt(stored, 10) : 0;
    } catch {
      return 0;
    }
  };

  const form = useForm<SignUpFormData>({
    resolver: zodResolver(signUpSchema),
    defaultValues: {
      email: "",
      password: "",
      treasurerName: "",
      brideName: "",
      groomName: "",
      treasurerPhone: "",
      weddingDate: "",
      venue: "",
      ...getPersistedData(), // Load persisted data
    },
    mode: "onChange",
  });

  // Initialize current step from localStorage and check for restored data
  useEffect(() => {
    const persistedStep = getPersistedStep();
    const persistedData = getPersistedData();

    setCurrentStep(persistedStep);

    // Check if we have any meaningful persisted data
    const hasData = Object.values(persistedData).some(value =>
      value && typeof value === 'string' && value.trim().length > 0
    );
    setHasRestoredData(hasData);
  }, []);

  // Persist form data whenever it changes
  useEffect(() => {
    const subscription = form.watch((data) => {
      try {
        // Only persist non-sensitive data (exclude password)
        const { password, ...dataToStore } = data;
        localStorage.setItem(FORM_STORAGE_KEY, JSON.stringify(dataToStore));
      } catch (error) {
        console.warn("Failed to persist form data:", error);
      }
    });

    return () => subscription.unsubscribe();
  }, [form]);

  // Persist current step whenever it changes
  useEffect(() => {
    try {
      localStorage.setItem(STEP_STORAGE_KEY, currentStep.toString());
    } catch (error) {
      console.warn("Failed to persist current step:", error);
    }
  }, [currentStep]);

  // Clear persisted data on successful submission
  const clearPersistedData = () => {
    try {
      localStorage.removeItem(FORM_STORAGE_KEY);
      localStorage.removeItem(STEP_STORAGE_KEY);
    } catch (error) {
      console.warn("Failed to clear persisted data:", error);
    }
  };

  // Clear form and start over
  const handleStartOver = () => {
    form.reset({
      email: "",
      password: "",
      treasurerName: "",
      brideName: "",
      groomName: "",
      treasurerPhone: "",
      weddingDate: "",
      venue: "",
    });
    setCurrentStep(0);
    setHasRestoredData(false);
    clearPersistedData();
  };

  const steps = [
    {
      id: "basic-auth",
      title: "Account",
      description: "Basic info",
    },
    {
      id: "wedding-details",
      title: "Wedding",
      description: "Couple details",
    },
    {
      id: "optional-info",
      title: "Optional",
      description: "Extra info",
    },
  ];

  const validateCurrentStep = async (): Promise<boolean> => {
    let schema: z.ZodSchema;
    let fieldsToValidate: (keyof SignUpFormData)[];

    switch (currentStep) {
      case 0:
        schema = step1Schema;
        fieldsToValidate = ["email", "password", "treasurerName"];
        break;
      case 1:
        schema = step2Schema;
        fieldsToValidate = ["brideName", "groomName"];
        break;
      case 2:
        schema = step3Schema;
        fieldsToValidate = ["treasurerPhone", "venue"];
        break;
      default:
        return false;
    }

    try {
      const currentValues = form.getValues();
      const stepData = Object.fromEntries(
        fieldsToValidate.map(field => [field, currentValues[field]])
      );
      
      await schema.parseAsync(stepData);
      
      // Clear any existing errors for this step
      fieldsToValidate.forEach(field => {
        form.clearErrors(field);
      });
      
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        // Set form errors
        error.errors.forEach((err) => {
          if (err.path[0] && fieldsToValidate.includes(err.path[0] as keyof SignUpFormData)) {
            form.setError(err.path[0] as keyof SignUpFormData, {
              type: "manual",
              message: err.message,
            });
          }
        });
      }
      return false;
    }
  };

  const handleNext = async () => {
    const isValid = await validateCurrentStep();
    if (isValid && currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);

      // Validate all steps before submission
      const isValid = await form.trigger();
      if (!isValid) {
        return;
      }

      const formData = form.getValues();
      await onSubmit(formData);

      // Clear persisted data on successful submission
      clearPersistedData();
    } catch (error) {
      console.error("Submission error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return <SignUpStep1 form={form} />;
      case 1:
        return <SignUpStep2 form={form} />;
      case 2:
        return <SignUpStep3 form={form} />;
      default:
        return null;
    }
  };

  // Check if current step has validation errors
  const hasStepErrors = () => {
    const errors = form.formState.errors;
    switch (currentStep) {
      case 0:
        return !!(errors.email || errors.password || errors.treasurerName);
      case 1:
        return !!(errors.brideName || errors.groomName);
      case 2:
        return !!(errors.treasurerPhone || errors.venue);
      default:
        return false;
    }
  };

  return (
    <Form {...form}>
      {/* Restored Data Indicator */}
      {hasRestoredData && (
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-start gap-3">
            <AlertCircle className="h-5 w-5 text-blue-600 flex-shrink-0 mt-0.5" />
            <div className="flex-1">
              <p className="text-sm font-medium text-blue-900">
                Previous form data restored
              </p>
              <p className="text-xs text-blue-700 mt-1">
                We've restored your previous progress. You can continue where you left off or start over.
              </p>
            </div>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleStartOver}
              className="flex items-center gap-1 text-xs"
            >
              <RotateCcw className="h-3 w-3" />
              Start Over
            </Button>
          </div>
        </div>
      )}

      <MultiStepForm
        steps={steps}
        currentStep={currentStep}
        onStepChange={setCurrentStep}
        onNext={handleNext}
        onPrevious={handlePrevious}
        onSubmit={handleSubmit}
        nextButtonText="Continue"
        submitButtonText="Create Account"
        isNextDisabled={hasStepErrors() || loading}
        isSubmitting={isSubmitting || loading}
        allowStepNavigation={true}
      >
        {renderCurrentStep()}
      </MultiStepForm>
    </Form>
  );
}
