# 📸 <PERSON><PERSON><PERSON>'s Image Feature

## Overview
The Couple's Image feature allows couples to upload and display their photo on their wedding pledge cards, making them more personal and engaging for guests.

## Features Implemented

### ✅ **Image Upload Component** (`src/components/ImageUpload.tsx`)
- **Drag & drop interface** for easy file upload
- **File validation** (JPEG, PNG, WebP, max 5MB)
- **Image preview** with hover controls
- **Replace/remove functionality**
- **Loading states** and error handling
- **Responsive design** for mobile and desktop

### ✅ **Database Integration**
- **New column**: `couple_image` in profiles table
- **Supabase Storage**: Dedicated `couple-images` bucket
- **Row Level Security**: Users can only manage their own images
- **Public access**: Images are publicly viewable for pledge cards

### ✅ **Profile Setup Integration**
- **Image upload section** in profile setup flow
- **Optional field** - couples can skip if desired
- **Form validation** and state management
- **Auto-save** with profile data

### ✅ **Profile Management Integration**
- **Image upload field** in profile edit form
- **Replace existing images** functionality
- **Form validation** with React Hook Form
- **Seamless user experience**

### ✅ **Pledge Card Display**
- **Circular image display** with elegant styling
- **Fallback to heart icon** when no image uploaded
- **Responsive sizing** (mobile: 128px, desktop: 160px)
- **Border and shadow effects** for visual appeal
- **Alt text** for accessibility

## Technical Implementation

### **Database Schema**
```sql
-- Added to profiles table
ALTER TABLE profiles 
ADD COLUMN couple_image TEXT;

-- Supabase Storage bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'couple-images',
  'couple-images', 
  true,
  5242880, -- 5MB
  ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
);
```

### **Storage Security Policies**
```sql
-- Users can upload their own images
CREATE POLICY "Users can upload their own couple images"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'couple-images' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- Public can view images (for pledge cards)
CREATE POLICY "Public can view couple images"
ON storage.objects FOR SELECT
USING (bucket_id = 'couple-images');
```

### **File Upload Process**
1. **File validation** (type, size)
2. **Generate unique filename** with user ID and timestamp
3. **Delete existing image** if replacing
4. **Upload to Supabase Storage**
5. **Get public URL**
6. **Update profile record**
7. **User feedback** via toast notifications

### **Image Display Logic**
```typescript
// In PledgeCard component
{pledgeData.coupleImage ? (
  <img
    src={pledgeData.coupleImage}
    alt={`${pledgeData.brideName} & ${pledgeData.groomName}`}
    className="w-32 h-32 md:w-40 md:h-40 rounded-full mx-auto object-cover border-4 border-white/30 shadow-lg"
  />
) : (
  <Heart className="h-16 w-16 mx-auto mb-6 opacity-80" />
)}
```

## User Experience

### **Upload Flow**
1. **Drag & drop or click** to select image
2. **Automatic validation** with helpful error messages
3. **Upload progress** indication
4. **Immediate preview** of uploaded image
5. **Success confirmation** via toast notification

### **Management Flow**
1. **Hover over image** to reveal controls
2. **Change button** to replace image
3. **Remove button** to delete image
4. **Confirmation** for destructive actions

### **Pledge Card Enhancement**
1. **Personal touch** with couple's photo
2. **Professional appearance** with circular crop
3. **Consistent styling** across all themes
4. **Graceful fallback** to heart icon

## File Specifications

### **Supported Formats**
- JPEG (.jpg, .jpeg)
- PNG (.png)
- WebP (.webp)

### **File Size Limits**
- **Maximum**: 5MB per image
- **Recommended**: 1-2MB for optimal performance
- **Dimensions**: Any size (auto-cropped to circular)

### **Storage Organization**
```
couple-images/
├── {user-id}/
│   └── couple-image-{timestamp}.{ext}
```

## Security Features

### **Access Control**
- **User isolation**: Users can only access their own images
- **Public viewing**: Images are publicly accessible for pledge cards
- **Secure upload**: Server-side validation and processing

### **File Validation**
- **MIME type checking**: Only allowed image formats
- **File size limits**: Prevents abuse and storage bloat
- **Filename sanitization**: Prevents path traversal attacks

## Performance Considerations

### **Optimization**
- **Lazy loading**: Images load when needed
- **CDN delivery**: Supabase Storage provides global CDN
- **Caching**: Browser and CDN caching for fast loading
- **Responsive images**: Appropriate sizing for device

### **Storage Management**
- **Automatic cleanup**: Old images deleted when replaced
- **Efficient naming**: Timestamp-based unique filenames
- **Folder organization**: User-based folder structure

## Future Enhancements

### **Planned Features**
- 📐 **Image cropping tool** for better control
- 🎨 **Image filters** and effects
- 📱 **Mobile camera integration**
- 🔄 **Multiple image support** (gallery)
- 📊 **Image analytics** (views, engagement)

### **Technical Improvements**
- 🗜️ **Automatic image compression**
- 🌐 **WebP conversion** for better performance
- 📱 **Progressive loading** for mobile
- 🔍 **Image optimization** based on usage

## Testing

### **Manual Testing Checklist**
- [ ] Upload various image formats (JPEG, PNG, WebP)
- [ ] Test file size limits (under and over 5MB)
- [ ] Verify drag & drop functionality
- [ ] Test image replacement
- [ ] Test image removal
- [ ] Verify pledge card display
- [ ] Test on mobile devices
- [ ] Check accessibility features

### **Edge Cases**
- [ ] Network interruption during upload
- [ ] Invalid file types
- [ ] Corrupted image files
- [ ] Very large images
- [ ] Very small images
- [ ] Special characters in filenames

## Integration Points

### **Components Updated**
- `src/pages/ProfileSetup.tsx` - Added image upload section
- `src/pages/Profile.tsx` - Added image management field
- `src/pages/PledgeCard.tsx` - Added image display logic
- `src/types/database.ts` - Updated type definitions

### **Database Changes**
- `supabase/migrations/20240320000007_add_couple_image.sql` - Schema update
- Storage bucket configuration
- RLS policies for security

## Benefits

### **For Couples**
- **Personalization**: Make pledge cards uniquely theirs
- **Professional appearance**: High-quality image display
- **Easy management**: Simple upload and replace process
- **Flexibility**: Optional feature, can skip if desired

### **For Guests**
- **Recognition**: Easily identify the couple
- **Engagement**: More personal connection to pledge card
- **Visual appeal**: Enhanced card appearance
- **Trust**: Authentic representation of the couple

## Success Metrics

### **Usage Metrics**
- **Upload rate**: % of users who upload images
- **Replacement rate**: How often users change images
- **File size distribution**: Average image sizes
- **Format preferences**: Most popular image formats

### **Performance Metrics**
- **Upload success rate**: % of successful uploads
- **Load times**: Image display performance
- **Error rates**: Upload and display failures
- **User satisfaction**: Feedback on image feature

---

**The Couple's Image feature significantly enhances the personalization and visual appeal of pledge cards, making them more engaging and memorable for wedding guests.** 📸💕
