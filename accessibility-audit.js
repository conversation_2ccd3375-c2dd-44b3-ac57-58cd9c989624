#!/usr/bin/env node

import fs from 'fs';
import path from 'path';

console.log('🔍 Accessibility Audit Report\n');

// Function to recursively find all component files
function findComponentFiles(dir) {
  let files = [];
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && item !== 'node_modules' && item !== '.git') {
      files = files.concat(findComponentFiles(fullPath));
    } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
      files.push(fullPath);
    }
  }
  
  return files;
}

// Accessibility patterns to check
const accessibilityChecks = {
  missingAltText: {
    pattern: /<img(?![^>]*alt=)/g,
    description: 'Images without alt text',
    severity: 'high'
  },
  missingAriaLabel: {
    pattern: /<button(?![^>]*aria-label)(?![^>]*aria-labelledby)/g,
    description: 'Buttons without aria-label or aria-labelledby',
    severity: 'medium'
  },
  missingFormLabels: {
    pattern: /<input(?![^>]*aria-label)(?![^>]*aria-labelledby)(?![^>]*id="[^"]*")(?![^>]*type="hidden")/g,
    description: 'Form inputs without proper labels',
    severity: 'high'
  },
  missingHeadingStructure: {
    pattern: /className="[^"]*text-\d+xl[^"]*"/g,
    description: 'Large text that might need semantic heading tags',
    severity: 'medium'
  },
  clickableWithoutRole: {
    pattern: /<div[^>]*onClick/g,
    description: 'Clickable divs without proper role',
    severity: 'high'
  },
  missingFocusManagement: {
    pattern: /tabIndex|autoFocus/g,
    description: 'Focus management patterns found',
    severity: 'info'
  },
  colorOnlyInformation: {
    pattern: /className="[^"]*text-(red|green|yellow|blue)-/g,
    description: 'Color-only information (needs additional indicators)',
    severity: 'medium'
  }
};

// Find all component files
const componentFiles = findComponentFiles('src');

let totalIssues = 0;
const issuesByFile = {};

console.log('📁 Scanning files...\n');

componentFiles.forEach(file => {
  const content = fs.readFileSync(file, 'utf8');
  const fileIssues = [];
  
  Object.entries(accessibilityChecks).forEach(([checkName, check]) => {
    const matches = content.match(check.pattern);
    if (matches) {
      fileIssues.push({
        type: checkName,
        description: check.description,
        severity: check.severity,
        count: matches.length,
        matches: matches.slice(0, 3) // Show first 3 matches
      });
      totalIssues += matches.length;
    }
  });
  
  if (fileIssues.length > 0) {
    issuesByFile[file] = fileIssues;
  }
});

// Report results
console.log('🚨 Accessibility Issues Found:\n');

Object.entries(issuesByFile).forEach(([file, issues]) => {
  console.log(`📄 ${file.replace(process.cwd() + path.sep, '')}`);
  
  issues.forEach(issue => {
    const severityIcon = {
      high: '🔴',
      medium: '🟡', 
      low: '🟢',
      info: 'ℹ️'
    }[issue.severity];
    
    console.log(`  ${severityIcon} ${issue.description} (${issue.count} instances)`);
    if (issue.matches.length > 0) {
      issue.matches.forEach(match => {
        console.log(`    └─ ${match.substring(0, 60)}...`);
      });
    }
  });
  console.log('');
});

// Summary
console.log('📊 Summary:');
console.log(`  - Total files scanned: ${componentFiles.length}`);
console.log(`  - Files with issues: ${Object.keys(issuesByFile).length}`);
console.log(`  - Total accessibility issues: ${totalIssues}`);

// Recommendations
console.log('\n💡 Recommendations:');
console.log('  1. Add alt text to all images');
console.log('  2. Ensure all interactive elements have proper ARIA labels');
console.log('  3. Use semantic HTML elements (button, input, etc.) instead of divs');
console.log('  4. Implement proper heading hierarchy (h1, h2, h3...)');
console.log('  5. Add focus management for modals and dynamic content');
console.log('  6. Provide non-color indicators for status information');
console.log('  7. Test with screen readers and keyboard navigation');

console.log('\n🔧 Next Steps:');
console.log('  - Run: npm install --save-dev @axe-core/react');
console.log('  - Add axe-core for automated accessibility testing');
console.log('  - Implement ARIA labels and roles');
console.log('  - Add keyboard navigation support');
