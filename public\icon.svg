<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="512" height="512">
  <defs>
    <linearGradient id="heartGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ec4899;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#be185d;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="256" cy="256" r="240" fill="#ffffff" stroke="#ec4899" stroke-width="8"/>
  
  <!-- Heart shape -->
  <path d="M256 448c-7.18 0-13.3-4.82-15.1-11.7l-112-448c-1.4-5.6 2.3-11.3 8.1-11.3h238c5.8 0 9.5 5.7 8.1 11.3l-112 448c-1.8 6.88-7.92 11.7-15.1 11.7z" fill="url(#heartGradient)" transform="translate(0, -32)"/>
  
  <!-- Simplified heart using path -->
  <path d="M256 416c-7.289 0-14.316-2.641-19.596-7.228-10.754-9.344-132.462-116.632-132.462-177.771 0-49.918 40.551-90.469 90.469-90.469 22.636 0 43.264 8.347 59.589 22.108 16.325-13.761 36.953-22.108 59.589-22.108 49.918 0 90.469 40.551 90.469 90.469 0 61.139-121.708 168.427-132.462 177.771-5.28 4.587-12.307 7.228-19.596 7.228z" fill="url(#heartGradient)"/>
  
  <!-- Ring elements -->
  <circle cx="200" cy="200" r="12" fill="#fbbf24" stroke="#f59e0b" stroke-width="2"/>
  <circle cx="312" cy="200" r="12" fill="#fbbf24" stroke="#f59e0b" stroke-width="2"/>
  
  <!-- Text "P4L" -->
  <text x="256" y="380" font-family="Arial, sans-serif" font-size="48" font-weight="bold" text-anchor="middle" fill="#ec4899">P4L</text>
</svg>
