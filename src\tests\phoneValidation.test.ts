import { normalizePhoneNumber, validatePhoneNumber } from '../types/app';
import { formatPhoneInput, validatePhoneInput } from '../utils/phoneFormatter';

describe('Phone Number Validation', () => {
  describe('normalizePhoneNumber', () => {
    test('should handle +256 format', () => {
      expect(normalizePhoneNumber('+256700123456')).toBe('+256700123456');
      expect(normalizePhoneNumber('+256 700 123 456')).toBe('+256700123456');
      expect(normalizePhoneNumber('+256-700-123-456')).toBe('+256700123456');
    });

    test('should handle 256 format', () => {
      expect(normalizePhoneNumber('256700123456')).toBe('+256700123456');
    });

    test('should handle 0xxx format', () => {
      expect(normalizePhoneNumber('0700123456')).toBe('+256700123456');
      expect(normalizePhoneNumber('0 700 123 456')).toBe('+256700123456');
    });

    test('should handle 9-digit format', () => {
      expect(normalizePhoneNumber('700123456')).toBe('+256700123456');
    });

    test('should return as-is for invalid formats', () => {
      expect(normalizePhoneNumber('123')).toBe('123');
      expect(normalizePhoneNumber('invalid')).toBe('invalid');
    });
  });

  describe('validatePhoneNumber', () => {
    test('should accept valid Ugandan phone numbers', () => {
      const validNumbers = [
        '+256700123456',
        '+256 700 123 456',
        '+256-700-123-456',
        '0700123456',
        '0 700 123 456',
        '700123456'
      ];

      validNumbers.forEach(number => {
        const result = validatePhoneNumber(number);
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
        expect(result.normalizedValue).toBe('+256700123456');
      });
    });

    test('should reject invalid phone numbers', () => {
      const invalidNumbers = [
        '+25670012345', // too short
        '+2567001234567', // too long
        '+1234567890', // wrong country code
        '+256123456789', // invalid prefix (starts with 1)
        '+256012345678', // invalid prefix (starts with 0)
        'invalid',
        '+256abc123456'
      ];

      invalidNumbers.forEach(number => {
        const result = validatePhoneNumber(number);
        if (result.isValid) {
          console.log(`Unexpected valid result for: ${number}`, result);
        }
        expect(result.isValid).toBe(false);
        expect(result.errors.length).toBeGreaterThan(0);
      });
    });

    test('should allow empty phone numbers', () => {
      const result = validatePhoneNumber('');
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
  });

  describe('formatPhoneInput', () => {
    test('should format +256 numbers with spaces', () => {
      expect(formatPhoneInput('+256700123456')).toBe('+256 700 123 456');
      expect(formatPhoneInput('+2567001234')).toBe('+256 700 123 4');
      expect(formatPhoneInput('+256700')).toBe('+256 700');
      expect(formatPhoneInput('+256')).toBe('+256 ');
    });

    test('should format 0xxx numbers with spaces', () => {
      expect(formatPhoneInput('0700123456')).toBe('0700 123 456');
      expect(formatPhoneInput('07001234')).toBe('0700 123 4');
      expect(formatPhoneInput('0700')).toBe('0700');
    });

    test('should auto-add +256 for digit-only input', () => {
      expect(formatPhoneInput('7')).toBe('+256 7');
      expect(formatPhoneInput('700')).toBe('+256 700');
    });

    test('should handle backspace correctly', () => {
      expect(formatPhoneInput('+256 70', '+256 700')).toBe('+256 70');
      expect(formatPhoneInput('0700 12', '0700 123')).toBe('0700 12');
    });
  });

  describe('validatePhoneInput', () => {
    test('should provide helpful feedback for incomplete numbers', () => {
      const result = validatePhoneInput('+256700123');
      expect(result.isValid).toBe(false);
      expect(result.message).toContain('Need 3 more digits');
    });

    test('should suggest corrections for common mistakes', () => {
      const result = validatePhoneInput('256700123456');
      expect(result.isValid).toBe(false);
      expect(result.message).toBe('Missing + prefix');
      expect(result.suggestion).toBe('Try: +256700123456');
    });

    test('should validate complete numbers', () => {
      const result = validatePhoneInput('+256700123456');
      expect(result.isValid).toBe(true);
      expect(result.message).toBeUndefined();
    });
  });
});
