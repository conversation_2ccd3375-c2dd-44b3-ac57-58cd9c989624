import { useOffline } from "@/hooks/use-offline";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { WifiOff, Wifi } from "lucide-react";

const OfflineIndicator = () => {
  const { isOffline, wasOffline, isOnline } = useOffline();

  if (isOffline) {
    return (
      <Alert
        className="fixed top-4 left-4 right-4 z-50 bg-red-50 border-red-200"
        role="alert"
        aria-live="assertive"
      >
        <WifiOff className="h-4 w-4 text-red-600" aria-hidden="true" />
        <AlertDescription className="text-red-800">
          You're currently offline. Some features may not be available.
        </AlertDescription>
      </Alert>
    );
  }

  if (wasOffline && isOnline) {
    return (
      <Alert
        className="fixed top-4 left-4 right-4 z-50 bg-green-50 border-green-200"
        role="alert"
        aria-live="polite"
      >
        <Wifi className="h-4 w-4 text-green-600" aria-hidden="true" />
        <AlertDescription className="text-green-800">
          Connection restored! You're back online.
        </AlertDescription>
      </Alert>
    );
  }

  return null;
};

export default OfflineIndicator;
