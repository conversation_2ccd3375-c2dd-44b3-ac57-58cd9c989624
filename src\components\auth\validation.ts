import { validatePhoneNumber, validateWeddingDate, ValidationError } from "@/types/app";
import type { SignUpFormData } from "./schemas";

export const validateSignUpData = (values: SignUpFormData) => {
  const errors: string[] = [];

  if (values.treasurerPhone) {
    const phoneValidation = validatePhoneNumber(values.treasurerPhone);
    if (!phoneValidation.isValid) {
      errors.push(...phoneValidation.errors);
    }
  }

  if (values.weddingDate) {
    const dateValidation = validateWeddingDate(values.weddingDate);
    if (!dateValidation.isValid) {
      errors.push(...dateValidation.errors);
    }
  }

  if (errors.length > 0) {
    throw new ValidationError(errors.join(', '));
  }
}; 