import { UseFormReturn } from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { SignUpFormData } from "@/components/auth/schemas";

interface SignUpStep1Props {
  form: UseFormReturn<SignUpFormData>;
}

export function SignUpStep1({ form }: SignUpStep1Props) {
  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-semibold text-foreground">Create Your Account</h2>
        <p className="text-muted-foreground mt-2">
          Let's start with your basic information to get you signed up
        </p>
      </div>

      <div className="space-y-4">
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email Address</FormLabel>
              <FormControl>
                <Input 
                  {...field} 
                  type="email" 
                  autoComplete="email"
                  placeholder="<EMAIL>"
                  className="h-11"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Password</FormLabel>
              <FormControl>
                <Input 
                  {...field} 
                  type="password" 
                  autoComplete="new-password"
                  placeholder="Create a secure password"
                  className="h-11"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="treasurerName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Your Name (Treasurer)</FormLabel>
              <FormControl>
                <Input 
                  {...field} 
                  placeholder="Your full name" 
                  autoComplete="name"
                  className="h-11"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="bg-muted/50 p-4 rounded-lg">
        <p className="text-sm text-muted-foreground">
          <strong>Next:</strong> We'll collect your wedding details to personalize your pledge experience.
        </p>
      </div>
    </div>
  );
}
