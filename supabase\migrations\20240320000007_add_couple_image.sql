-- Add couple_image field to profiles table
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS couple_image TEXT;

-- Create storage bucket for couple images
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'couple-images',
  'couple-images',
  true,
  5242880, -- 5MB limit
  ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
) ON CONFLICT (id) DO NOTHING;

-- Set up RLS policies for couple images bucket
CREATE POLICY "Users can upload their own couple images"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'couple-images' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Users can view their own couple images"
ON storage.objects FOR SELECT
USING (
  bucket_id = 'couple-images' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Users can update their own couple images"
ON storage.objects FOR UPDATE
USING (
  bucket_id = 'couple-images' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Users can delete their own couple images"
ON storage.objects FOR DELETE
USING (
  bucket_id = 'couple-images' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- Allow public access to couple images for pledge cards
CREATE POLICY "Public can view couple images"
ON storage.objects FOR SELECT
USING (bucket_id = 'couple-images');

-- Update the profiles table comment
COMMENT ON COLUMN profiles.couple_image IS 'URL to the couple''s image stored in Supabase Storage';

-- Create index for faster image lookups
CREATE INDEX IF NOT EXISTS idx_profiles_couple_image ON profiles(couple_image) WHERE couple_image IS NOT NULL;
