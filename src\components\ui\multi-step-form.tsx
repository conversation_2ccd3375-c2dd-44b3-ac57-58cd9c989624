import * as React from "react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { StepIndicator, type Step } from "@/components/ui/step-indicator";
import { ChevronLeft, ChevronRight } from "lucide-react";

interface MultiStepFormProps {
  steps: Step[];
  currentStep: number;
  onStepChange: (step: number) => void;
  onNext?: () => void | Promise<void>;
  onPrevious?: () => void;
  onSubmit?: () => void | Promise<void>;
  children: React.ReactNode;
  className?: string;
  showStepIndicator?: boolean;
  showNavigation?: boolean;
  nextButtonText?: string;
  previousButtonText?: string;
  submitButtonText?: string;
  isNextDisabled?: boolean;
  isSubmitting?: boolean;
  allowStepNavigation?: boolean;
}

const MultiStepForm = React.forwardRef<HTMLDivElement, MultiStepFormProps>(
  ({
    steps,
    currentStep,
    onStepChange,
    onNext,
    onPrevious,
    onSubmit,
    children,
    className,
    showStepIndicator = true,
    showNavigation = true,
    nextButtonText = "Continue",
    previousButtonText = "Back",
    submitButtonText = "Submit",
    isNextDisabled = false,
    isSubmitting = false,
    allowStepNavigation = true,
  }, ref) => {
    const isFirstStep = currentStep === 0;
    const isLastStep = currentStep === steps.length - 1;

    const handleStepClick = (stepIndex: number) => {
      if (allowStepNavigation && stepIndex <= currentStep) {
        onStepChange(stepIndex);
      }
    };

    const handleNext = async () => {
      if (isLastStep && onSubmit) {
        await onSubmit();
      } else if (onNext) {
        await onNext();
      }
    };

    const handlePrevious = () => {
      if (onPrevious) {
        onPrevious();
      } else if (!isFirstStep) {
        onStepChange(currentStep - 1);
      }
    };

    const handleKeyDown = (event: React.KeyboardEvent) => {
      if (event.key === "Enter" && !isNextDisabled) {
        event.preventDefault();
        handleNext();
      } else if (event.key === "Escape" && !isFirstStep) {
        event.preventDefault();
        handlePrevious();
      }
    };

    // Update steps with current state
    const updatedSteps = steps.map((step, index) => ({
      ...step,
      isCompleted: index < currentStep,
      isActive: index === currentStep,
      isClickable: allowStepNavigation && index <= currentStep,
    }));

    return (
      <div 
        ref={ref} 
        className={cn("w-full max-w-2xl mx-auto", className)}
        onKeyDown={handleKeyDown}
      >
        {/* Step Indicator */}
        {showStepIndicator && (
          <div className="mb-8">
            <StepIndicator
              steps={updatedSteps}
              currentStep={currentStep}
              onStepClick={handleStepClick}
            />
          </div>
        )}

        {/* Form Content */}
        <div className="mb-8">
          <div className="min-h-[400px]">
            {children}
          </div>
        </div>

        {/* Navigation */}
        {showNavigation && (
          <div className="flex justify-between items-center pt-6 border-t">
            <div>
              {!isFirstStep && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={handlePrevious}
                  disabled={isSubmitting}
                  className="flex items-center gap-2"
                >
                  <ChevronLeft className="h-4 w-4" />
                  {previousButtonText}
                </Button>
              )}
            </div>

            <div className="flex items-center gap-4">
              {/* Step Counter */}
              <span className="text-sm text-muted-foreground">
                Step {currentStep + 1} of {steps.length}
              </span>

              {/* Next/Submit Button */}
              <Button
                type={isLastStep ? "submit" : "button"}
                onClick={handleNext}
                disabled={isNextDisabled || isSubmitting}
                className="flex items-center gap-2 min-w-[120px]"
              >
                {isSubmitting ? (
                  "Processing..."
                ) : isLastStep ? (
                  submitButtonText
                ) : (
                  <>
                    {nextButtonText}
                    <ChevronRight className="h-4 w-4" />
                  </>
                )}
              </Button>
            </div>
          </div>
        )}
      </div>
    );
  }
);

MultiStepForm.displayName = "MultiStepForm";

export { MultiStepForm };
export type { MultiStepFormProps };
