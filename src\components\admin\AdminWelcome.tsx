import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { 
  Shield, 
  Users, 
  UserPlus, 
  Settings, 
  BarChart3, 
  Database,
  ArrowRight,
  CheckCircle
} from 'lucide-react';

const AdminWelcome = () => {
  const navigate = useNavigate();

  const adminFeatures = [
    {
      icon: <Users className="h-6 w-6 text-blue-600" />,
      title: "User Management",
      description: "View, edit, suspend, and delete user accounts with complete profile access"
    },
    {
      icon: <UserPlus className="h-6 w-6 text-green-600" />,
      title: "Account Creation",
      description: "Create new user accounts with full wedding profile setup"
    },
    {
      icon: <Shield className="h-6 w-6 text-purple-600" />,
      title: "Admin Rights",
      description: "Promote users to admin or demote existing administrators"
    },
    {
      icon: <BarChart3 className="h-6 w-6 text-orange-600" />,
      title: "System Analytics",
      description: "Monitor user statistics, activity, and platform usage"
    },
    {
      icon: <Database className="h-6 w-6 text-red-600" />,
      title: "Data Management",
      description: "Access all user data, pledges, and system information"
    },
    {
      icon: <Settings className="h-6 w-6 text-gray-600" />,
      title: "System Control",
      description: "Full administrative control over the PledgeForLove platform"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12">
      <div className="max-w-6xl mx-auto px-6">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <div className="bg-blue-100 p-4 rounded-full">
              <Shield className="h-12 w-12 text-blue-600" />
            </div>
          </div>
          <h1 className="text-4xl font-bold text-blue-900 mb-4">
            Welcome, Administrator
          </h1>
          <p className="text-xl text-blue-700 max-w-3xl mx-auto">
            You have full administrative access to the PledgeForLove platform. 
            Manage users, monitor system activity, and maintain the platform with powerful admin tools.
          </p>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          <Card className="border-blue-200 hover:shadow-lg transition-shadow cursor-pointer"
                onClick={() => navigate('/admin/panel')}>
            <CardContent className="p-6 text-center">
              <Users className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-blue-900 mb-2">Manage Users</h3>
              <p className="text-blue-700 text-sm mb-4">
                Access the full admin panel to manage all user accounts
              </p>
              <Button className="w-full">
                Open Admin Panel
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </CardContent>
          </Card>

          <Card className="border-green-200 hover:shadow-lg transition-shadow cursor-pointer"
                onClick={() => navigate('/admin/panel')}>
            <CardContent className="p-6 text-center">
              <UserPlus className="h-12 w-12 text-green-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-green-900 mb-2">Create Users</h3>
              <p className="text-green-700 text-sm mb-4">
                Add new user accounts with complete profile setup
              </p>
              <Button variant="outline" className="w-full border-green-300 text-green-700 hover:bg-green-50">
                Create New User
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </CardContent>
          </Card>

          <Card className="border-purple-200 hover:shadow-lg transition-shadow cursor-pointer"
                onClick={() => navigate('/admin-setup')}>
            <CardContent className="p-6 text-center">
              <Shield className="h-12 w-12 text-purple-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-purple-900 mb-2">Admin Setup</h3>
              <p className="text-purple-700 text-sm mb-4">
                Manage admin privileges and create additional administrators
              </p>
              <Button variant="outline" className="w-full border-purple-300 text-purple-700 hover:bg-purple-50">
                Admin Settings
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Admin Features */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="text-2xl text-blue-900">Administrative Capabilities</CardTitle>
            <CardDescription>
              As an administrator, you have access to these powerful features
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {adminFeatures.map((feature, index) => (
                <div key={index} className="flex items-start gap-4 p-4 rounded-lg bg-gray-50">
                  <div className="flex-shrink-0">
                    {feature.icon}
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">{feature.title}</h4>
                    <p className="text-sm text-gray-600">{feature.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Admin Notes */}
        <Card className="bg-blue-50 border-blue-200">
          <CardHeader>
            <CardTitle className="text-blue-900 flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              Administrator Notes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-blue-800">
              <p className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 mt-0.5 text-blue-600" />
                <span className="text-sm">
                  <strong>Profile Not Required:</strong> As an administrator, you don't need to create a wedding profile. 
                  Your role is to manage the system, not create pledge cards.
                </span>
              </p>
              <p className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 mt-0.5 text-blue-600" />
                <span className="text-sm">
                  <strong>Full System Access:</strong> You have complete access to all user data, 
                  system settings, and administrative functions.
                </span>
              </p>
              <p className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 mt-0.5 text-blue-600" />
                <span className="text-sm">
                  <strong>Security Responsibility:</strong> Keep your admin credentials secure and 
                  only grant admin access to trusted individuals.
                </span>
              </p>
              <p className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 mt-0.5 text-blue-600" />
                <span className="text-sm">
                  <strong>User Support:</strong> You can assist users with account issues, 
                  profile problems, and system-related questions.
                </span>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdminWelcome;
