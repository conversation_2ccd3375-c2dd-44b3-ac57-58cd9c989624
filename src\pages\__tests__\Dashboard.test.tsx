import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { vi } from 'vitest';
import Dashboard from '../Dashboard';

// Mock react-router-dom
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

// Mock Supabase
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    auth: {
      getSession: vi.fn(),
      onAuthStateChange: vi.fn(() => ({
        data: { subscription: { unsubscribe: vi.fn() } },
      })),
    },
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          single: vi.fn(),
          order: vi.fn(() => ({
            limit: vi.fn(),
          })),
        })),
      })),
    })),
  },
}));

// Mock toast
vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({ toast: vi.fn() }),
}));

// Mock child components
vi.mock('@/components/PledgeManagement', () => ({
  default: ({ userId }: { userId: string }) => (
    <div data-testid="pledge-management">Pledge Management for {userId}</div>
  ),
}));

vi.mock('@/components/ProfileManagement', () => ({
  default: ({ profile, onProfileUpdate }: any) => (
    <div data-testid="profile-management">
      Profile Management for {profile?.treasurer_name}
      <button onClick={() => onProfileUpdate({ ...profile, updated: true })}>
        Update Profile
      </button>
    </div>
  ),
}));

vi.mock('@/components/ui/loading-spinner', () => ({
  default: ({ text }: { text: string }) => <div data-testid="loading-spinner">{text}</div>,
}));

const mockProfile = {
  id: 'test-user-id',
  treasurer_name: 'John Doe',
  bride_name: 'Jane',
  groom_name: 'John',
  wedding_date: '2024-12-25',
  venue: 'Test Venue',
  treasurer_phone: '+256777123456',
  theme: 'sunset',
  special_message: 'Test message',
};

const mockPledges = [
  {
    id: 1,
    guest_name: 'Guest 1',
    amount_pledged: 100000,
    pledge_date: '2024-01-01',
    payment_status: 'pending',
  },
  {
    id: 2,
    guest_name: 'Guest 2',
    amount_pledged: 200000,
    pledge_date: '2024-01-02',
    payment_status: 'paid',
  },
];

const renderDashboard = () => {
  return render(
    <BrowserRouter>
      <Dashboard />
    </BrowserRouter>
  );
};

describe('Dashboard Page', () => {
  let supabase: any;

  beforeEach(async () => {
    vi.clearAllMocks();
    const module = await import('@/integrations/supabase/client');
    supabase = module.supabase;

    // Default successful session
    supabase.auth.getSession.mockResolvedValue({
      data: { session: { user: { id: 'test-user-id' } } },
    });
  });

  it('shows loading spinner while fetching data', () => {
    // Mock delayed response
    supabase.from.mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockImplementation(
            () => new Promise(resolve => setTimeout(() => resolve({ data: mockProfile }), 100))
          ),
        }),
      }),
    });

    renderDashboard();

    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
    expect(screen.getByText('Loading your dashboard...')).toBeInTheDocument();
  });

  it('renders dashboard with profile data', async () => {
    supabase.from.mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ data: mockProfile, error: null }),
          order: vi.fn().mockReturnValue({
            limit: vi.fn().mockResolvedValue({ data: mockPledges, error: null }),
          }),
        }),
      }),
    });

    renderDashboard();

    await waitFor(() => {
      expect(screen.getByText('Dashboard')).toBeInTheDocument();
      expect(screen.getByText('Welcome, John Doe! Manage your wedding pledges and profile settings here.')).toBeInTheDocument();
    });
  });

  it('displays wedding information in summary cards', async () => {
    supabase.from.mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ data: mockProfile, error: null }),
          order: vi.fn().mockReturnValue({
            limit: vi.fn().mockResolvedValue({ data: mockPledges, error: null }),
          }),
        }),
      }),
    });

    renderDashboard();

    await waitFor(() => {
      expect(screen.getByText('Jane & John')).toBeInTheDocument();
      expect(screen.getByText('12/25/2024')).toBeInTheDocument();
    });
  });

  it('shows recent pledges in overview tab', async () => {
    supabase.from.mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ data: mockProfile, error: null }),
          order: vi.fn().mockReturnValue({
            limit: vi.fn().mockResolvedValue({ data: mockPledges, error: null }),
          }),
        }),
      }),
    });

    renderDashboard();

    await waitFor(() => {
      expect(screen.getByText('Recent Pledges')).toBeInTheDocument();
      expect(screen.getByText('Guest 1')).toBeInTheDocument();
      expect(screen.getByText('UGX 100,000')).toBeInTheDocument();
    });
  });

  it('switches between tabs correctly', async () => {
    mockSupabase.from.mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ data: mockProfile, error: null }),
          order: vi.fn().mockReturnValue({
            limit: vi.fn().mockResolvedValue({ data: mockPledges, error: null }),
          }),
        }),
      }),
    });
    
    renderDashboard();
    
    await waitFor(() => {
      expect(screen.getByText('Recent Pledges')).toBeInTheDocument();
    });
    
    // Switch to pledges tab
    const pledgesTab = screen.getByText('Pledges');
    fireEvent.click(pledgesTab);
    
    expect(screen.getByTestId('pledge-management')).toBeInTheDocument();
    expect(screen.getByText('Pledge Management for test-user-id')).toBeInTheDocument();
  });

  it('switches to profile tab', async () => {
    mockSupabase.from.mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ data: mockProfile, error: null }),
          order: vi.fn().mockReturnValue({
            limit: vi.fn().mockResolvedValue({ data: mockPledges, error: null }),
          }),
        }),
      }),
    });
    
    renderDashboard();
    
    await waitFor(() => {
      expect(screen.getByText('Recent Pledges')).toBeInTheDocument();
    });
    
    // Switch to profile tab
    const profileTab = screen.getByText('Profile');
    fireEvent.click(profileTab);
    
    expect(screen.getByTestId('profile-management')).toBeInTheDocument();
    expect(screen.getByText('Profile Management for John Doe')).toBeInTheDocument();
  });

  it('handles profile update', async () => {
    mockSupabase.from.mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ data: mockProfile, error: null }),
          order: vi.fn().mockReturnValue({
            limit: vi.fn().mockResolvedValue({ data: mockPledges, error: null }),
          }),
        }),
      }),
    });
    
    renderDashboard();
    
    await waitFor(() => {
      expect(screen.getByText('Recent Pledges')).toBeInTheDocument();
    });
    
    // Switch to profile tab
    const profileTab = screen.getByText('Profile');
    fireEvent.click(profileTab);
    
    // Update profile
    const updateButton = screen.getByText('Update Profile');
    fireEvent.click(updateButton);
    
    // Profile should be updated (this would trigger a re-render in real app)
    expect(screen.getByTestId('profile-management')).toBeInTheDocument();
  });

  it('shows error message when profile fails to load', async () => {
    mockSupabase.from.mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ data: null, error: { message: 'Profile not found' } }),
        }),
      }),
    });
    
    renderDashboard();
    
    await waitFor(() => {
      expect(screen.getByText('Error loading dashboard. Please try again.')).toBeInTheDocument();
    });
  });

  it('redirects to auth when no session', async () => {
    mockSupabase.auth.getSession.mockResolvedValue({ data: { session: null } });
    
    renderDashboard();
    
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/auth');
    });
  });

  it('shows "No pledges yet" when no recent pledges', async () => {
    mockSupabase.from.mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({ data: mockProfile, error: null }),
          order: vi.fn().mockReturnValue({
            limit: vi.fn().mockResolvedValue({ data: [], error: null }),
          }),
        }),
      }),
    });
    
    renderDashboard();
    
    await waitFor(() => {
      expect(screen.getByText('No pledges yet')).toBeInTheDocument();
    });
  });
});
