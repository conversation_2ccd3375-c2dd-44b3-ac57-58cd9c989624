import * as React from "react";
import { cn } from "@/lib/utils";
import { Check } from "lucide-react";

export interface Step {
  id: string;
  title: string;
  description?: string;
  isCompleted?: boolean;
  isActive?: boolean;
  isClickable?: boolean;
}

interface StepIndicatorProps {
  steps: Step[];
  currentStep: number;
  onStepClick?: (stepIndex: number) => void;
  className?: string;
}

const StepIndicator = React.forwardRef<HTMLDivElement, StepIndicatorProps>(
  ({ steps, currentStep, onStepClick, className }, ref) => {
    return (
      <div ref={ref} className={cn("w-full", className)}>
        {/* Progress Bar */}
        <div className="relative mb-8">
          <div className="absolute top-4 left-0 w-full h-0.5 bg-muted">
            <div 
              className="h-full bg-primary transition-all duration-300 ease-in-out"
              style={{ 
                width: `${((currentStep) / (steps.length - 1)) * 100}%` 
              }}
            />
          </div>
          
          {/* Step Circles */}
          <div className="relative flex justify-between">
            {steps.map((step, index) => {
              const isCompleted = index < currentStep;
              const isActive = index === currentStep;
              const isClickable = step.isClickable && (isCompleted || index <= currentStep);
              
              return (
                <button
                  key={step.id}
                  onClick={() => isClickable && onStepClick?.(index)}
                  disabled={!isClickable}
                  className={cn(
                    "relative flex h-8 w-8 items-center justify-center rounded-full border-2 transition-all duration-200",
                    "focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
                    {
                      // Completed step
                      "border-primary bg-primary text-primary-foreground": isCompleted,
                      // Active step
                      "border-primary bg-background text-primary": isActive && !isCompleted,
                      // Future step
                      "border-muted-foreground/30 bg-background text-muted-foreground": !isActive && !isCompleted,
                      // Clickable states
                      "cursor-pointer hover:border-primary/80": isClickable,
                      "cursor-not-allowed": !isClickable,
                    }
                  )}
                  aria-label={`Step ${index + 1}: ${step.title}`}
                  aria-current={isActive ? "step" : undefined}
                >
                  {isCompleted ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    <span className="text-sm font-medium">{index + 1}</span>
                  )}
                </button>
              );
            })}
          </div>
        </div>

        {/* Step Labels */}
        <div className="flex justify-between">
          {steps.map((step, index) => {
            const isCompleted = index < currentStep;
            const isActive = index === currentStep;
            
            return (
              <div 
                key={`${step.id}-label`}
                className="flex flex-col items-center text-center max-w-[120px]"
              >
                <span 
                  className={cn(
                    "text-sm font-medium transition-colors",
                    {
                      "text-primary": isActive || isCompleted,
                      "text-muted-foreground": !isActive && !isCompleted,
                    }
                  )}
                >
                  {step.title}
                </span>
                {step.description && (
                  <span 
                    className={cn(
                      "text-xs mt-1 transition-colors",
                      {
                        "text-muted-foreground": isActive || isCompleted,
                        "text-muted-foreground/60": !isActive && !isCompleted,
                      }
                    )}
                  >
                    {step.description}
                  </span>
                )}
              </div>
            );
          })}
        </div>
      </div>
    );
  }
);

StepIndicator.displayName = "StepIndicator";

export { StepIndicator };
export type { StepIndicatorProps };
