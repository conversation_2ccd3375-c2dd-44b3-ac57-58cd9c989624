/**
 * Beta Testing Monitoring Utilities
 * Tracks user behavior, performance, and issues during beta testing
 */

interface BetaEvent {
  type: 'pageview' | 'error' | 'performance' | 'user_action' | 'feedback';
  timestamp: string;
  userId?: string;
  sessionId: string;
  data: Record<string, any>;
  userAgent: string;
  url: string;
}

interface PerformanceMetrics {
  loadTime: number;
  domContentLoaded: number;
  firstContentfulPaint?: number;
  largestContentfulPaint?: number;
  cumulativeLayoutShift?: number;
}

interface UserFeedback {
  rating: number;
  category: 'bug' | 'feature' | 'usability' | 'performance' | 'general';
  description: string;
  page: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

class BetaMonitoring {
  private sessionId: string;
  private userId?: string;
  private isEnabled: boolean;
  private events: BetaEvent[] = [];

  constructor() {
    this.sessionId = this.generateSessionId();
    this.isEnabled = import.meta.env.VITE_BETA_MODE === 'true';
    
    if (this.isEnabled) {
      this.initializeMonitoring();
    }
  }

  private generateSessionId(): string {
    return `beta_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private initializeMonitoring(): void {
    // Track page views
    this.trackPageView();
    
    // Track performance metrics
    this.trackPerformance();
    
    // Track errors
    this.trackErrors();
    
    // Track user interactions
    this.trackUserInteractions();
    
    // Send events periodically
    setInterval(() => this.flushEvents(), 30000); // Every 30 seconds
    
    // Send events before page unload
    window.addEventListener('beforeunload', () => this.flushEvents());
  }

  setUserId(userId: string): void {
    this.userId = userId;
  }

  private trackPageView(): void {
    const event: BetaEvent = {
      type: 'pageview',
      timestamp: new Date().toISOString(),
      userId: this.userId,
      sessionId: this.sessionId,
      data: {
        path: window.location.pathname,
        search: window.location.search,
        referrer: document.referrer,
        title: document.title
      },
      userAgent: navigator.userAgent,
      url: window.location.href
    };
    
    this.addEvent(event);
  }

  private trackPerformance(): void {
    // Track initial page load performance
    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      const metrics: PerformanceMetrics = {
        loadTime: navigation.loadEventEnd - navigation.loadEventStart,
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart
      };

      // Track Core Web Vitals if available
      if ('web-vitals' in window) {
        this.trackWebVitals(metrics);
      }

      const event: BetaEvent = {
        type: 'performance',
        timestamp: new Date().toISOString(),
        userId: this.userId,
        sessionId: this.sessionId,
        data: {
          metrics,
          connectionType: (navigator as any).connection?.effectiveType,
          deviceMemory: (navigator as any).deviceMemory
        },
        userAgent: navigator.userAgent,
        url: window.location.href
      };
      
      this.addEvent(event);
    });
  }

  private trackWebVitals(metrics: PerformanceMetrics): void {
    // This would integrate with web-vitals library if installed
    // For now, we'll track basic metrics
    try {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'paint' && entry.name === 'first-contentful-paint') {
            metrics.firstContentfulPaint = entry.startTime;
          }
          if (entry.entryType === 'largest-contentful-paint') {
            metrics.largestContentfulPaint = entry.startTime;
          }
        }
      });
      
      observer.observe({ entryTypes: ['paint', 'largest-contentful-paint'] });
    } catch (error) {
      console.warn('Performance observer not supported:', error);
    }
  }

  private trackErrors(): void {
    window.addEventListener('error', (event) => {
      const errorEvent: BetaEvent = {
        type: 'error',
        timestamp: new Date().toISOString(),
        userId: this.userId,
        sessionId: this.sessionId,
        data: {
          message: event.message,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          stack: event.error?.stack,
          severity: 'high'
        },
        userAgent: navigator.userAgent,
        url: window.location.href
      };
      
      this.addEvent(errorEvent);
    });

    window.addEventListener('unhandledrejection', (event) => {
      const errorEvent: BetaEvent = {
        type: 'error',
        timestamp: new Date().toISOString(),
        userId: this.userId,
        sessionId: this.sessionId,
        data: {
          message: 'Unhandled Promise Rejection',
          reason: event.reason?.toString(),
          stack: event.reason?.stack,
          severity: 'medium'
        },
        userAgent: navigator.userAgent,
        url: window.location.href
      };
      
      this.addEvent(errorEvent);
    });
  }

  private trackUserInteractions(): void {
    // Track button clicks
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      if (target.tagName === 'BUTTON' || target.closest('button')) {
        const button = target.tagName === 'BUTTON' ? target : target.closest('button');
        
        const actionEvent: BetaEvent = {
          type: 'user_action',
          timestamp: new Date().toISOString(),
          userId: this.userId,
          sessionId: this.sessionId,
          data: {
            action: 'click',
            element: 'button',
            text: button?.textContent?.trim(),
            id: button?.id,
            className: button?.className
          },
          userAgent: navigator.userAgent,
          url: window.location.href
        };
        
        this.addEvent(actionEvent);
      }
    });

    // Track form submissions
    document.addEventListener('submit', (event) => {
      const form = event.target as HTMLFormElement;
      
      const actionEvent: BetaEvent = {
        type: 'user_action',
        timestamp: new Date().toISOString(),
        userId: this.userId,
        sessionId: this.sessionId,
        data: {
          action: 'form_submit',
          element: 'form',
          id: form.id,
          className: form.className,
          formAction: form.action
        },
        userAgent: navigator.userAgent,
        url: window.location.href
      };
      
      this.addEvent(actionEvent);
    });
  }

  trackCustomEvent(eventType: string, data: Record<string, any>): void {
    if (!this.isEnabled) return;

    const event: BetaEvent = {
      type: 'user_action',
      timestamp: new Date().toISOString(),
      userId: this.userId,
      sessionId: this.sessionId,
      data: {
        customEvent: eventType,
        ...data
      },
      userAgent: navigator.userAgent,
      url: window.location.href
    };
    
    this.addEvent(event);
  }

  submitFeedback(feedback: UserFeedback): void {
    if (!this.isEnabled) return;

    const event: BetaEvent = {
      type: 'feedback',
      timestamp: new Date().toISOString(),
      userId: this.userId,
      sessionId: this.sessionId,
      data: feedback,
      userAgent: navigator.userAgent,
      url: window.location.href
    };
    
    this.addEvent(event);
    this.flushEvents(); // Send feedback immediately
  }

  private addEvent(event: BetaEvent): void {
    this.events.push(event);
    
    // If it's a critical error, send immediately
    if (event.type === 'error' && event.data.severity === 'critical') {
      this.flushEvents();
    }
  }

  private async flushEvents(): Promise<void> {
    if (this.events.length === 0) return;

    const eventsToSend = [...this.events];
    this.events = [];

    try {
      // In a real implementation, this would send to your analytics endpoint
      console.log('Beta Monitoring Events:', eventsToSend);
      
      // For beta testing, we'll store in localStorage as backup
      const existingEvents = JSON.parse(localStorage.getItem('betaEvents') || '[]');
      const allEvents = [...existingEvents, ...eventsToSend];
      localStorage.setItem('betaEvents', JSON.stringify(allEvents.slice(-1000))); // Keep last 1000 events
      
      // Simulate API call (replace with actual endpoint)
      if (import.meta.env.VITE_BETA_ANALYTICS_ENDPOINT) {
        await fetch(import.meta.env.VITE_BETA_ANALYTICS_ENDPOINT, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(eventsToSend),
        });
      }
    } catch (error) {
      console.error('Failed to send beta monitoring events:', error);
      // Re-add events to queue for retry
      this.events.unshift(...eventsToSend);
    }
  }

  // Get stored events for debugging
  getStoredEvents(): BetaEvent[] {
    return JSON.parse(localStorage.getItem('betaEvents') || '[]');
  }

  // Clear stored events
  clearStoredEvents(): void {
    localStorage.removeItem('betaEvents');
  }
}

// Create singleton instance
export const betaMonitoring = new BetaMonitoring();

// Export types for use in components
export type { BetaEvent, PerformanceMetrics, UserFeedback };
