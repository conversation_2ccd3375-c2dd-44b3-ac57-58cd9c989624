# 🧪 Testing Pledge Export Functionality

## Quick Test Guide

### **Prerequisites**
- Application running at `http://localhost:8081/`
- User account with completed profile
- Some pledge data (can be test data)

### **Test Steps**

#### **1. Access Pledge Management**
1. Navigate to `http://localhost:8081/`
2. Sign in with your account
3. Go to **Dashboard** → **Pledges** tab
4. Verify you can see the pledge management interface

#### **2. Test Quick Export**
1. Look for the **Export** button (download icon) in the top-right of the Pledge Management card
2. Click the **Export** button
3. Select **Export to Excel (.xlsx)**
4. Verify file downloads automatically
5. Open the downloaded file and check:
   - ✅ Pledges sheet with all pledge data
   - ✅ Summary sheet with statistics
   - ✅ Proper formatting and column widths

#### **3. Test CSV Export**
1. Click **Export** button again
2. Select **Export to CSV**
3. Verify CSV file downloads
4. Open in spreadsheet application and verify data format

#### **4. Test Advanced Export**
1. Click **Export** → **Advanced Export...**
2. Test custom filename: Enter "test_export"
3. Toggle "Include summary sheet" off/on
4. Test status filters:
   - Check only "Completed payments"
   - Click **Export Excel**
   - Verify only completed pledges are exported
5. Test with multiple status filters selected

#### **5. Test Edge Cases**
1. **Empty pledges**: If no pledges exist, verify appropriate message
2. **No filtered results**: Set filters that return no results
3. **Large datasets**: Test with many pledges (if available)

### **Expected Results**

#### **Excel Export (.xlsx)**
- **File naming**: `pledges_export_YYYY-MM-DD.xlsx` (or custom name)
- **Sheet 1 - Pledges**: 
  - Columns: No., Guest Name, Phone Number, Amount Pledged, Amount Paid, Balance, Payment Status, Pledge Date, Payment Date, Notes
  - Proper formatting with readable column widths
  - Correct data types and formatting
- **Sheet 2 - Summary** (if enabled):
  - Total pledges count
  - Financial totals and percentages
  - Status breakdown
  - Export metadata

#### **CSV Export**
- **File naming**: `pledges_export_YYYY-MM-DD.csv` (or custom name)
- **Content**: Same data as Excel pledges sheet in CSV format
- **Encoding**: UTF-8 with proper quote handling

#### **Advanced Export**
- **Custom filename**: Uses specified filename
- **Status filtering**: Only exports selected payment statuses
- **Summary toggle**: Includes/excludes summary sheet as specified
- **Preview**: Shows accurate count of records to be exported

### **Browser Console Checks**
Monitor browser console for:
- ✅ No JavaScript errors during export
- ✅ Success messages for completed exports
- ✅ Proper error handling for failed exports

### **File Verification Checklist**

#### **Excel File (.xlsx)**
- [ ] File opens without errors
- [ ] Pledges sheet contains all expected columns
- [ ] Data is properly formatted (numbers, dates, text)
- [ ] Summary sheet shows correct calculations
- [ ] Column widths are readable
- [ ] No missing or corrupted data

#### **CSV File**
- [ ] File opens in spreadsheet applications
- [ ] All data is properly quoted and escaped
- [ ] Special characters display correctly
- [ ] No data truncation or corruption

### **Performance Testing**
- **Small dataset** (1-10 pledges): Export should be instant
- **Medium dataset** (10-100 pledges): Export within 1-2 seconds
- **Large dataset** (100+ pledges): Export within 5 seconds

### **Error Scenarios to Test**
1. **No pledges**: Should show appropriate message
2. **Network issues**: Should handle gracefully
3. **Browser restrictions**: Should work in different browsers
4. **File permissions**: Should handle download restrictions

### **Cross-Browser Testing**
Test export functionality in:
- ✅ Chrome/Chromium
- ✅ Firefox
- ✅ Safari (if available)
- ✅ Edge

### **Mobile Testing**
- Test export on mobile devices
- Verify download behavior on mobile browsers
- Check responsive design of export dialog

## Automated Testing

Run the export utility tests:
```bash
npm test src/utils/__tests__/exportUtils.test.ts
```

Expected output:
```
✓ exportUtils > exportPledgesToXLSX > should export pledges to XLSX format successfully
✓ exportUtils > exportPledgesToXLSX > should handle custom filename
✓ exportUtils > exportPledgesToXLSX > should handle empty pledges array
✓ exportUtils > exportPledgesToCSV > should export pledges to CSV format successfully
✓ exportUtils > exportPledgesToCSV > should handle custom filename
✓ exportUtils > exportPledgesWithOptions > should filter pledges by status
✓ exportUtils > exportPledgesWithOptions > should filter pledges by date range
✓ exportUtils > exportPledgesWithOptions > should apply multiple filters
```

## Troubleshooting

### **Common Issues**
1. **Export button not visible**: Check if user has pledges and proper permissions
2. **Download not starting**: Check browser download settings and popup blockers
3. **File corruption**: Verify browser supports modern file download APIs
4. **Missing data**: Check pledge data integrity and export filters

### **Debug Steps**
1. Open browser developer tools
2. Check console for error messages
3. Verify network requests complete successfully
4. Test with different browsers
5. Clear browser cache and try again

## Success Criteria

✅ **Quick Export**: Both Excel and CSV exports work with one click
✅ **Advanced Export**: All filtering and customization options work
✅ **File Quality**: Generated files are properly formatted and complete
✅ **User Experience**: Export process is intuitive and provides feedback
✅ **Error Handling**: Graceful handling of edge cases and errors
✅ **Performance**: Exports complete quickly even with larger datasets
✅ **Cross-Platform**: Works across different browsers and devices

---

**Note**: The export feature is now fully integrated and ready for production use!
