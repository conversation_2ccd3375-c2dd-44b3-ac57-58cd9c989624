import * as React from "react";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { formatPhoneInput, getPhonePlaceholder, validatePhoneInput } from "@/utils/phoneFormatter";
import { normalizePhoneNumber } from "@/types/app";

export interface PhoneInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange' | 'value'> {
  value?: string;
  onChange?: (value: string, normalizedValue: string) => void;
  showValidation?: boolean;
}

const PhoneInput = React.forwardRef<HTMLInputElement, PhoneInputProps>(
  ({ className, value = '', onChange, showValidation = false, ...props }, ref) => {
    const [previousValue, setPreviousValue] = React.useState('');
    const [focused, setFocused] = React.useState(false);
    
    const validation = React.useMemo(() => {
      if (!showValidation || !value.trim()) return null;
      return validatePhoneInput(value);
    }, [value, showValidation]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value;
      const formattedValue = formatPhoneInput(newValue, previousValue);
      const normalizedValue = normalizePhoneNumber(formattedValue);
      
      setPreviousValue(value);
      onChange?.(formattedValue, normalizedValue);
    };

    const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
      setFocused(true);
      props.onFocus?.(e);
    };

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      setFocused(false);
      props.onBlur?.(e);
    };

    const placeholder = React.useMemo(() => {
      return props.placeholder || getPhonePlaceholder(value);
    }, [props.placeholder, value]);

    return (
      <div className="space-y-1">
        <Input
          {...props}
          ref={ref}
          type="tel"
          value={value}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          className={cn(
            className,
            validation && !validation.isValid && focused && "border-yellow-500 focus:border-yellow-500",
            validation && validation.isValid && value.trim() && "border-green-500 focus:border-green-500"
          )}
          autoComplete="tel"
        />
        
        {showValidation && validation && !validation.isValid && focused && (
          <div className="text-xs space-y-1">
            {validation.message && (
              <p className="text-yellow-600">{validation.message}</p>
            )}
            {validation.suggestion && (
              <p className="text-gray-500">💡 {validation.suggestion}</p>
            )}
          </div>
        )}
        
        {showValidation && validation && validation.isValid && value.trim() && focused && (
          <p className="text-xs text-green-600">✓ Valid phone number</p>
        )}
      </div>
    );
  }
);

PhoneInput.displayName = "PhoneInput";

export { PhoneInput };
