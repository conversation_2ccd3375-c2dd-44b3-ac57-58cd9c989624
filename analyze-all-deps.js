#!/usr/bin/env node

import fs from 'fs';
import path from 'path';

// Get all dependencies from package.json
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const allDeps = {
  ...packageJson.dependencies,
  ...packageJson.devDependencies
};

console.log('📦 Analyzing all dependencies...\n');

// Function to recursively find all files
function findFiles(dir, extensions = ['.tsx', '.ts', '.js', '.jsx']) {
  let files = [];
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && item !== 'node_modules' && item !== '.git') {
      files = files.concat(findFiles(fullPath, extensions));
    } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
      files.push(fullPath);
    }
  }
  
  return files;
}

// Find all source files
const sourceFiles = findFiles('.');

// Track which packages are used
const usedPackages = new Set();

// Special packages that are used indirectly
const indirectlyUsed = new Set([
  'typescript',
  'vite',
  '@vitejs/plugin-react-swc',
  'tailwindcss',
  'autoprefixer',
  'postcss',
  'eslint',
  '@eslint/js',
  'typescript-eslint',
  'eslint-plugin-react-hooks',
  'eslint-plugin-react-refresh',
  'globals',
  'vitest',
  'jsdom',
  '@testing-library/jest-dom',
  '@testing-library/react',
  '@testing-library/user-event',
  '@types/node',
  '@types/react',
  '@types/react-dom',
  '@tailwindcss/typography',
  'lovable-tagger'
]);

// Analyze each file
sourceFiles.forEach(file => {
  const content = fs.readFileSync(file, 'utf8');
  
  Object.keys(allDeps).forEach(pkg => {
    // Check for direct imports
    if (content.includes(`from "${pkg}"`) || 
        content.includes(`from '${pkg}'`) ||
        content.includes(`import("${pkg}")`) ||
        content.includes(`import('${pkg}')`) ||
        content.includes(`require("${pkg}")`) ||
        content.includes(`require('${pkg}')`)) {
      usedPackages.add(pkg);
    }
  });
});

// Add indirectly used packages
indirectlyUsed.forEach(pkg => {
  if (allDeps[pkg]) {
    usedPackages.add(pkg);
  }
});

const unusedPackages = Object.keys(allDeps).filter(pkg => !usedPackages.has(pkg));

console.log('✅ Used packages:');
Array.from(usedPackages).sort().forEach(pkg => {
  console.log(`  - ${pkg}`);
});

console.log(`\n❌ Potentially unused packages (${unusedPackages.length}):`);
unusedPackages.forEach(pkg => {
  console.log(`  - ${pkg}`);
});

console.log(`\n📊 Summary:`);
console.log(`  - Used: ${usedPackages.size}/${Object.keys(allDeps).length} packages`);
console.log(`  - Potentially unused: ${unusedPackages.length}/${Object.keys(allDeps).length} packages`);

if (unusedPackages.length > 0) {
  console.log(`\n⚠️  Manual review needed for these packages before removal:`);
  unusedPackages.forEach(pkg => {
    console.log(`  - ${pkg} (check if used in config files or build process)`);
  });
}
