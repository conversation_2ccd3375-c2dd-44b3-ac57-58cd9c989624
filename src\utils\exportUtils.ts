import * as XLSX from 'xlsx';
import type { Pledge } from '@/types/app';

/**
 * Export pledges data to XLSX format
 * @param pledges - Array of pledge data
 * @param filename - Optional filename (defaults to auto-generated name)
 * @param includeMetadata - Whether to include metadata sheet
 */
export const exportPledgesToXLSX = (
  pledges: Pledge[], 
  filename?: string,
  includeMetadata: boolean = true
) => {
  try {
    // Create a new workbook
    const workbook = XLSX.utils.book_new();

    // Prepare pledge data for export
    const pledgeData = pledges.map((pledge, index) => ({
      'No.': index + 1,
      'Guest Name': pledge.guest_name,
      'Phone Number': pledge.guest_phone || 'Not provided',
      'Amount Pledged (UGX)': pledge.amount_pledged,
      'Amount Paid (UGX)': pledge.amount_paid,
      'Balance (UGX)': pledge.amount_pledged - pledge.amount_paid,
      'Payment Status': pledge.payment_status,
      'Pledge Date': new Date(pledge.pledge_date).toLocaleDateString(),
      'Payment Date': pledge.payment_date ? new Date(pledge.payment_date).toLocaleDateString() : 'Not paid',
      'Notes': pledge.notes || 'No notes'
    }));

    // Create the main pledges worksheet
    const pledgeWorksheet = XLSX.utils.json_to_sheet(pledgeData);

    // Set column widths for better readability
    const columnWidths = [
      { wch: 5 },   // No.
      { wch: 20 },  // Guest Name
      { wch: 15 },  // Phone Number
      { wch: 18 },  // Amount Pledged
      { wch: 15 },  // Amount Paid
      { wch: 15 },  // Balance
      { wch: 15 },  // Payment Status
      { wch: 12 },  // Pledge Date
      { wch: 12 },  // Payment Date
      { wch: 30 }   // Notes
    ];
    pledgeWorksheet['!cols'] = columnWidths;

    // Add the pledges worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, pledgeWorksheet, 'Pledges');

    // Add summary/metadata sheet if requested
    if (includeMetadata) {
      const totalPledged = pledges.reduce((sum, pledge) => sum + pledge.amount_pledged, 0);
      const totalPaid = pledges.reduce((sum, pledge) => sum + pledge.amount_paid, 0);
      const totalBalance = totalPledged - totalPaid;
      
      const statusCounts = pledges.reduce((acc, pledge) => {
        acc[pledge.payment_status] = (acc[pledge.payment_status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const summaryData = [
        { 'Metric': 'Total Pledges', 'Value': pledges.length },
        { 'Metric': 'Total Amount Pledged (UGX)', 'Value': totalPledged.toLocaleString() },
        { 'Metric': 'Total Amount Paid (UGX)', 'Value': totalPaid.toLocaleString() },
        { 'Metric': 'Outstanding Balance (UGX)', 'Value': totalBalance.toLocaleString() },
        { 'Metric': 'Collection Rate (%)', 'Value': totalPledged > 0 ? ((totalPaid / totalPledged) * 100).toFixed(2) + '%' : '0%' },
        { 'Metric': '', 'Value': '' }, // Empty row
        { 'Metric': 'Payment Status Breakdown', 'Value': '' },
        { 'Metric': 'Pending Payments', 'Value': statusCounts.pending || 0 },
        { 'Metric': 'Partial Payments', 'Value': statusCounts.partial || 0 },
        { 'Metric': 'Completed Payments', 'Value': statusCounts.completed || 0 },
        { 'Metric': '', 'Value': '' }, // Empty row
        { 'Metric': 'Export Date', 'Value': new Date().toLocaleString() },
        { 'Metric': 'Generated by', 'Value': 'PledgeForLove Uganda' }
      ];

      const summaryWorksheet = XLSX.utils.json_to_sheet(summaryData);
      summaryWorksheet['!cols'] = [{ wch: 25 }, { wch: 20 }];
      
      XLSX.utils.book_append_sheet(workbook, summaryWorksheet, 'Summary');
    }

    // Generate filename if not provided
    const exportFilename = filename || `pledges_export_${new Date().toISOString().split('T')[0]}.xlsx`;

    // Write and download the file
    XLSX.writeFile(workbook, exportFilename);

    return {
      success: true,
      filename: exportFilename,
      recordCount: pledges.length
    };
  } catch (error) {
    console.error('Error exporting pledges to XLSX:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Export filtered pledges with custom options
 * @param pledges - Array of pledge data
 * @param options - Export options
 */
export interface ExportOptions {
  filename?: string;
  includeMetadata?: boolean;
  dateRange?: {
    start: Date;
    end: Date;
  };
  statusFilter?: string[];
  customFields?: {
    label: string;
    value: (pledge: Pledge) => string | number;
  }[];
}

export const exportPledgesWithOptions = (pledges: Pledge[], options: ExportOptions = {}) => {
  let filteredPledges = [...pledges];

  // Apply date range filter if specified
  if (options.dateRange) {
    filteredPledges = filteredPledges.filter(pledge => {
      const pledgeDate = new Date(pledge.pledge_date);
      return pledgeDate >= options.dateRange!.start && pledgeDate <= options.dateRange!.end;
    });
  }

  // Apply status filter if specified
  if (options.statusFilter && options.statusFilter.length > 0) {
    filteredPledges = filteredPledges.filter(pledge => 
      options.statusFilter!.includes(pledge.payment_status)
    );
  }

  return exportPledgesToXLSX(
    filteredPledges, 
    options.filename, 
    options.includeMetadata ?? true
  );
};

/**
 * Generate a quick CSV export for simple use cases
 * @param pledges - Array of pledge data
 * @param filename - Optional filename
 */
export const exportPledgesToCSV = (pledges: Pledge[], filename?: string) => {
  try {
    const headers = [
      'Guest Name',
      'Phone Number', 
      'Amount Pledged (UGX)',
      'Amount Paid (UGX)',
      'Balance (UGX)',
      'Payment Status',
      'Pledge Date',
      'Payment Date',
      'Notes'
    ];

    const csvData = pledges.map(pledge => [
      pledge.guest_name,
      pledge.guest_phone || 'Not provided',
      pledge.amount_pledged,
      pledge.amount_paid,
      pledge.amount_pledged - pledge.amount_paid,
      pledge.payment_status,
      new Date(pledge.pledge_date).toLocaleDateString(),
      pledge.payment_date ? new Date(pledge.payment_date).toLocaleDateString() : 'Not paid',
      pledge.notes || 'No notes'
    ]);

    const csvContent = [headers, ...csvData]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    
    link.setAttribute('href', url);
    link.setAttribute('download', filename || `pledges_export_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    return {
      success: true,
      filename: filename || `pledges_export_${new Date().toISOString().split('T')[0]}.csv`,
      recordCount: pledges.length
    };
  } catch (error) {
    console.error('Error exporting pledges to CSV:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};
