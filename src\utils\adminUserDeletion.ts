import { supabase } from '@/integrations/supabase/client';

/**
 * Admin function to delete a user and all associated data
 * Note: This requires service role key, not anon key
 */
export const adminDeleteUser = async (userId: string) => {
  try {
    // 1. Delete all pledges for this user
    const { error: pledgesError } = await supabase
      .from('pledges')
      .delete()
      .eq('user_id', userId);

    if (pledgesError) {
      console.error('Error deleting pledges:', pledgesError);
      throw pledgesError;
    }

    // 2. Delete user profile
    const { error: profileError } = await supabase
      .from('profiles')
      .delete()
      .eq('id', userId);

    if (profileError) {
      console.error('Error deleting profile:', profileError);
      throw profileError;
    }

    // 3. Delete user from auth (requires service role)
    // This would need to be done via Supabase Admin API or Edge Function
    console.log(`User ${userId} data deleted successfully`);
    
    return { success: true };
  } catch (error) {
    console.error('Error in admin user deletion:', error);
    throw error;
  }
};

/**
 * Get all users for admin management
 * Note: This is a simplified version - you'd want proper pagination and filtering
 */
export const getAllUsers = async () => {
  try {
    const { data: profiles, error } = await supabase
      .from('profiles')
      .select(`
        id,
        bride_name,
        groom_name,
        treasurer_name,
        email,
        created_at,
        updated_at
      `)
      .order('created_at', { ascending: false });

    if (error) throw error;

    return profiles;
  } catch (error) {
    console.error('Error fetching users:', error);
    throw error;
  }
};
