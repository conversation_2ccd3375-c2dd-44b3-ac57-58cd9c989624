
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Heart, Share2, ArrowLeft } from "lucide-react";

interface PledgeThankYouProps {
  pledgeData: {
    brideName: string;
    groomName: string;
    theme: string;
    coupleImage?: string | null;
  };
  guestName: string;
  pledgeAmount: number;
  onReset: () => void;
  onShare: () => void;
}

const PledgeThankYou = ({ pledgeData, guestName, pledgeAmount, onReset, onShare }: PledgeThankYouProps) => {
  const themes = {
    sunset: "from-orange-400 to-pink-500",
    royal: "from-purple-500 to-blue-600",
    garden: "from-green-400 to-teal-500",
    golden: "from-yellow-400 to-orange-500"
  };

  return (
    <Card className="bg-white/90 backdrop-blur-sm overflow-hidden">
      <div className={`bg-gradient-to-br ${themes[pledgeData.theme]} p-8 text-white text-center`}>
        {pledgeData.coupleImage ? (
          <div className="mb-6">
            <img
              src={pledgeData.coupleImage}
              alt={`${pledgeData.brideName} & ${pledgeData.groomName}`}
              className="w-32 h-32 md:w-40 md:h-40 rounded-full mx-auto object-cover border-4 border-white/30 shadow-lg"
            />
          </div>
        ) : (
          <Heart className="h-16 w-16 mx-auto mb-6 opacity-80" />
        )}
        <h1 className="text-3xl font-bold mb-4">Thank You, {guestName}! 🎉</h1>
        <p className="text-xl opacity-90 mb-4">
          Your pledge of UGX {pledgeAmount.toLocaleString()} has been recorded
        </p>
        <div className="bg-white/20 backdrop-blur-sm rounded-lg p-6">
          <p className="text-lg leading-relaxed">
            {pledgeData.brideName} & {pledgeData.groomName} are grateful for your generous support. 
            Your contribution will help make their special day even more memorable.
          </p>
        </div>
      </div>
      
      <CardContent className="p-8 text-center">
        <h2 className="text-2xl font-bold text-gray-800 mb-4">What's Next?</h2>
        <p className="text-gray-600 mb-6">
          The treasurer will contact you regarding payment details. You can also share this pledge card with other family and friends.
        </p>
        
        <div className="flex gap-4 justify-center">
          <Button onClick={onShare} variant="outline" className="flex-1 max-w-48">
            <Share2 className="h-4 w-4 mr-2" />
            Share Card
          </Button>
          <Button onClick={onReset} className="flex-1 max-w-48">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Make Another Pledge
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default PledgeThankYou;
