
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Check<PERSON>ir<PERSON>, Clock, DollarSign } from "lucide-react";

interface PledgeStatsProps {
  totalPledged: number;
  totalPaid: number;
  pendingAmount: number;
}

const PledgeStats = ({ totalPledged, totalPaid, pendingAmount }: PledgeStatsProps) => {
  return (
    <div className="grid md:grid-cols-3 gap-4">
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium">Total Pledged</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2">
            <DollarSign className="h-5 w-5 text-gray-600" aria-hidden="true" />
            <div className="text-2xl font-bold">UGX {totalPledged.toLocaleString()}</div>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium">Total Paid</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-600" aria-hidden="true" />
            <div className="text-2xl font-bold text-green-600" aria-label={`Total paid: UGX ${totalPaid.toLocaleString()}`}>
              UGX {totalPaid.toLocaleString()}
            </div>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium">Pending</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-orange-600" aria-hidden="true" />
            <div className="text-2xl font-bold text-orange-600" aria-label={`Pending amount: UGX ${pendingAmount.toLocaleString()}`}>
              UGX {pendingAmount.toLocaleString()}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PledgeStats;
