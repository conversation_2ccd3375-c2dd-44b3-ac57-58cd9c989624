-- Enable Row Level Security
ALTER TABLE pledges ENABLE ROW LEVEL SECURITY;

-- Create policies for pledges table
CREATE POLICY "Users can view their own pledges"
  ON pledges FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own pledges"
  ON pledges FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own pledges"
  ON pledges FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own pledges"
  ON pledges FOR DELETE
  USING (auth.uid() = user_id);

-- Grant necessary permissions to authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON pledges TO authenticated;
GRANT USAGE ON SEQUENCE pledges_id_seq TO authenticated; 